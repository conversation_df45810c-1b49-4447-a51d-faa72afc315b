using WebApplication_HM.Interface;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.DTOs.RequestDTO;
using SqlSugar;
using WebApplication_HM.Models;
using System.Collections.Generic;
using WebApplication_HM.Services.SocketInfo;
using Microsoft.Extensions.Logging;
using WebApplication_HM.DTOs;
using PetSkillDetailDTO = WebApplication_HM.DTOs.PetSkillDetailDTO;
using Newtonsoft.Json;

namespace WebApplication_HM.Services
{
    public class PlayerService : IPlayerService
    {
        private readonly ISqlSugarClient _db;
        private readonly IRealTimeService _realTimeService;
        private readonly ILogger<PlayerService> _logger;
        private readonly WebAntiCheatService _antiCheatService;
        private readonly ILevelService _levelService;
        private readonly ISkillService _skillService;
        private readonly IGameEventTriggerService _gameEventTrigger;

        // 临时存储战斗奖励信息（安全机制）
        private static readonly Dictionary<string, BattleReward> _battleRewards = new();
        private static readonly object _rewardLock = new object();
        public PlayerService(ISqlSugarClient db, IRealTimeService realTimeService, ILogger<PlayerService> logger, WebAntiCheatService antiCheatService, ILevelService levelService, ISkillService skillService, IGameEventTriggerService gameEventTrigger)
        {
            _db = db;
            _realTimeService = realTimeService;
            _logger = logger;
            _antiCheatService = antiCheatService;
            _levelService = levelService;
            _skillService = skillService;
            _gameEventTrigger = gameEventTrigger;
        }

        /// <summary>
        /// 用户登录，校验账号密码，返回登录结果
        /// </summary>
        /// <param name="request">登录请求参数</param>
        /// <returns>登录结果</returns>
        public LoginResultDTO Login(LoginRequestDTO request)
        {
            // 查询用户表，校验账号和密码
            var user = _db.Queryable<user>().Where(x => x.username == request.Number && x.password == request.Password).First();
            if (user == null)
            {
                return new LoginResultDTO
                {
                    Success = false,
                    Message = "账号或密码错误"
                };
            }
            return new LoginResultDTO
            {
                Success = true,
                UserId = user.id,
                NickName = user.nickname,
                Message = "登录成功"
            };
        }

        /// <summary>
        /// 玩家注册
        /// </summary>
        /// <param name="request">注册请求参数</param>
        /// <returns>注册结果</returns>
        public RegisterResultDTO Register(RegisterRequestDTO request)
        {
            try
            {
                // 验证账号是否已存在
                var existingUser = _db.Queryable<user>()
                    .Where(x => x.username == request.Account)
                    .First();

                if (existingUser != null)
                {
                    return new RegisterResultDTO
                    {
                        Success = false,
                        Message = "账号已存在"
                    };
                }

                // 创建新用户
                var newUser = new user
                {
                    username = request.Account,
                    password = request.Password, // 在生产环境中应该加密
                    nickname = request.Nickname,
                    gold = 10000, // 初始金币
                    yuanbao = 100, // 初始元宝
                    vip_level = 1, // 初始等级
                    create_time = DateTime.Now,
                    reg_time = DateTime.Now
                };

                // 插入用户记录
                var userId = _db.Insertable(newUser).ExecuteReturnIdentity();

                if (userId > 0)
                {
                    // TODO: 可以在这里初始化玩家数据，比如赠送初始宠物
                    
                    return new RegisterResultDTO
                    {
                        Success = true,
                        Message = "注册成功",
                        UserId = userId,
                        Account = request.Account,
                        Nickname = request.Nickname
                    };
                }
                else
                {
                    return new RegisterResultDTO
                    {
                        Success = false,
                        Message = "注册失败，请稍后重试"
                    };
                }
            }
            catch (Exception ex)
            {
                return new RegisterResultDTO
                {
                    Success = false,
                    Message = $"注册失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取玩家基础信息
        /// </summary>
        /// <param name="request">玩家信息查询请求</param>
        /// <returns>玩家信息结果</returns>
        public PlayerInfoResultDTO GetPlayerInfo(PlayerInfoRequestDTO request)
        {
            try
            {
                var user = _db.Queryable<Models.user>()
                    .Where(x => x.id == request.UserId)
                    .First();

                if (user == null)
                {
                    return new PlayerInfoResultDTO
                    {
                        Success = false,
                        Message = "用户不存在"
                    };
                }

                var playerInfo = new PlayerInfoDTO
                {
                    UserId = user.id,
                    Account = user.username,
                    Nickname = user.nickname ?? "新玩家",
                    Sex = user.sex, // 添加性别字段
                    Level = user.vip_level ?? 1, // 暂时使用VIP等级作为玩家等级
                    Experience = 0, // user表中没有经验字段，先设为0
                    Gold = user.gold ?? 0,
                    Diamond = user.yuanbao ?? 0, // 使用元宝作为钻石
                    crystal = user.crystal ?? 0, // 水晶
                    MainPetId = user.main_pet_id,
                    CreateTime = user.create_time ?? DateTime.Now,
                    LastLoginTime = user.reg_time ?? DateTime.Now // 暂时使用注册时间
                };

                return new PlayerInfoResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    PlayerInfo = playerInfo
                };
            }
            catch (Exception ex)
            {
                return new PlayerInfoResultDTO
                {
                    Success = false,
                    Message = $"获取玩家信息失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 领取战斗奖励
        /// </summary>
        /// <param name="request">领取奖励请求</param>
        /// <returns>奖励结果</returns>
        public async Task<ClaimRewardResultDTO> ClaimBattleReward(ClaimRewardRequestDTO request)
        {
            try
            {
                // 验证BattleId
                if (string.IsNullOrEmpty(request.BattleId))
                {
                    return new ClaimRewardResultDTO
                    {
                        Success = false,
                        Message = "战斗ID不能为空"
                    };
                }

                // 从临时存储中获取奖励信息
                BattleReward battleReward;
                lock (_rewardLock)
                {
                    if (!_battleRewards.TryGetValue(request.BattleId, out battleReward))
                    {
                        return new ClaimRewardResultDTO
                        {
                            Success = false,
                            Message = "战斗奖励不存在或已过期，请重新战斗"
                        };
                    }

                    // 验证用户ID匹配
                    if (battleReward.UserId != request.UserId)
                    {
                        return new ClaimRewardResultDTO
                        {
                            Success = false,
                            Message = "无权领取此战斗奖励"
                        };
                    }

                    // 验证是否胜利
                    if (!battleReward.IsWin)
                    {
                        return new ClaimRewardResultDTO
                        {
                            Success = false,
                            Message = "战斗失败，无法获得奖励"
                        };
                    }

                    // 移除已使用的奖励记录（防止重复领取）
                    _battleRewards.Remove(request.BattleId);
                }

                // 获取用户信息
                var user = _db.Queryable<user>()
                    .Where(x => x.id == battleReward.UserId)
                    .First();

                if (user == null)
                {
                    return new ClaimRewardResultDTO
                    {
                        Success = false,
                        Message = "用户不存在"
                    };
                }

                // 获取宠物信息
                var pet = _db.Queryable<user_pet>()
                    .Where(x => x.user_id == battleReward.UserId && x.id == battleReward.PetId)
                    .First();

                if (pet == null)
                {
                    return new ClaimRewardResultDTO
                    {
                        Success = false,
                        Message = "宠物不存在"
                    };
                }

                var result = new ClaimRewardResultDTO
                {
                    Success = true,
                    ExperienceGained = battleReward.Experience,
                    GoldGained = battleReward.Gold,
                    YuanbaoGained = battleReward.Yuanbao
                };

                // 记录升级前的等级
                int oldPlayerLevel = user.vip_level ?? 1; // 暂时使用VIP等级
                int oldPetLevel = CalculatePetLevel(pet.exp ?? 0);

                // 开启事务处理奖励发放
                _db.Ado.BeginTran();

                try
                {
                    // 更新玩家金币和元宝（暂时不更新经验，因为user表没有exp字段）
                    _db.Updateable<user>()
                        .SetColumns(x => new user
                        {
                            gold = (user.gold ?? 0) + battleReward.Gold,
                            yuanbao = (user.yuanbao ?? 0) + battleReward.Yuanbao
                        })
                        .Where(x => x.id == battleReward.UserId)
                        .ExecuteCommand();

                    // 更新宠物经验
                    _db.Updateable<user_pet>()
                        .SetColumns(x => new user_pet
                        {
                            exp = (pet.exp ?? 0) + battleReward.Experience
                        })
                        .Where(x => x.id == battleReward.PetId)
                        .ExecuteCommand();

                    // 计算新等级（暂时不计算玩家等级提升，因为user表没有exp字段）
                    int newPlayerLevel = oldPlayerLevel; // 暂时不升级
                    int newPetLevel = await CalculatePetLevelAsync((pet.exp ?? 0) + battleReward.Experience);

                    // 检查是否升级
                    if (newPlayerLevel > oldPlayerLevel)
                    {
                        result.PlayerLeveledUp = true;
                        result.NewPlayerLevel = newPlayerLevel;

                        // 更新玩家等级（暂时使用VIP等级）
                        _db.Updateable<user>()
                            .SetColumns(x => new user { vip_level = newPlayerLevel })
                            .Where(x => x.id == battleReward.UserId)
                            .ExecuteCommand();
                    }

                    if (newPetLevel > oldPetLevel)
                    {
                        result.PetLeveledUp = true;
                        result.NewPetLevel = newPetLevel;

                        // 升级时提升宠物属性
                        var levelDiff = newPetLevel - oldPetLevel;
                        _db.Updateable<user_pet>()
                            .SetColumns(x => new user_pet
                            {
                                atk = (pet.atk ?? 0) + (levelDiff * 5),
                                def = (pet.def ?? 0) + (levelDiff * 3),
                                hp = (pet.hp ?? 0) + (levelDiff * 10),
                                mp = (pet.mp ?? 0) + (levelDiff * 5),
                                spd = (pet.spd ?? 0) + (levelDiff * 2)
                            })
                            .Where(x => x.id == battleReward.PetId)
                            .ExecuteCommand();
                    }

                    // 处理掉落物品
                    foreach (var dropItem in battleReward.DropItems)
                    {
                        if (!string.IsNullOrEmpty(dropItem))
                        {
                            // 解析掉落物品格式：itemId*count
                            var parts = dropItem.Split('*');
                            if (parts.Length == 2 && int.TryParse(parts[1], out int count))
                            {
                                AddOrUpdateUserItem(battleReward.UserId, parts[0], count);

                                // 添加到结果中显示给用户
                                result.ItemsGained.Add(new RewardItemDTO
                                {
                                    ItemId = int.TryParse(parts[0], out int itemId) ? itemId : 0,
                                    ItemName = GetItemName(parts[0]),
                                    Quantity = count
                                });
                            }
                        }
                    }

                    // 🎯 记录怪物击杀信息到数据库
                    try
                    {
                        // 检查是否已经存在该用户对该怪物的击杀记录
                        var existingRecord = await _db.Queryable<monster_kill_record>()
                            .Where(x => x.user_id == battleReward.UserId && x.monster_id == battleReward.MonsterId)
                            .FirstAsync();

                        if (existingRecord != null)
                        {
                            // 如果存在记录，则增加击杀数量
                            await _db.Updateable<monster_kill_record>()
                                .SetColumns(x => new monster_kill_record
                                {
                                    kill_quantity = existingRecord.kill_quantity + 1,
                                    kill_time = DateTime.Now,  // 更新最后击杀时间
                                    battle_id = battleReward.BattleId  // 更新最新的战斗ID
                                })
                                .Where(x => x.record_id == existingRecord.record_id)
                                .ExecuteCommandAsync();

                            _logger.LogInformation("击杀记录已更新: UserId={UserId}, MonsterId={MonsterId}, 新击杀数量={KillQuantity}, BattleId={BattleId}",
                                battleReward.UserId, battleReward.MonsterId, existingRecord.kill_quantity + 1, battleReward.BattleId);
                        }
                        else
                        {
                            // 如果不存在记录，则新增记录
                            var killRecord = new monster_kill_record
                            {
                                user_id = battleReward.UserId,
                                monster_id = battleReward.MonsterId,
                                battle_id = battleReward.BattleId,
                                kill_time = DateTime.Now,
                                kill_quantity = 1,  // 初始击杀数量为1
                                settlement_quantity = 0  // 初始结算数量为0
                            };

                            await _db.Insertable(killRecord).ExecuteCommandAsync();
                            _logger.LogInformation("新击杀记录已保存: UserId={UserId}, MonsterId={MonsterId}, BattleId={BattleId}",
                                battleReward.UserId, battleReward.MonsterId, battleReward.BattleId);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "保存击杀记录失败: UserId={UserId}, MonsterId={MonsterId}",
                            battleReward.UserId, battleReward.MonsterId);
                        // 不影响主流程，只记录错误
                    }

                    _db.Ado.CommitTran();

                    // 构建奖励消息
                    var rewardParts = new List<string>();
                    if (result.ExperienceGained > 0) rewardParts.Add($"{result.ExperienceGained}经验值");
                    if (result.GoldGained > 0) rewardParts.Add($"{result.GoldGained}金币");
                    if (result.YuanbaoGained > 0) rewardParts.Add($"{result.YuanbaoGained}元宝");
                    if (result.ItemsGained.Count > 0)
                    {
                        var itemNames = result.ItemsGained.Select(x => $"{x.ItemName}x{x.Quantity}");
                        rewardParts.Add(string.Join("、", itemNames));
                    }

                    result.Message = $"奖励领取成功！获得{string.Join("、", rewardParts)}";
                    if (result.PlayerLeveledUp || result.PetLeveledUp)
                    {
                        result.Message += "，恭喜升级！";
                    }

                    // 🎯 触发任务事件
                    try
                    {
                        // 触发金币变化事件
                        if (battleReward.Gold > 0)
                        {
                            var newGoldAmount = (user.gold ?? 0) + battleReward.Gold;
                            await _gameEventTrigger.OnGoldChangedAsync(battleReward.UserId, newGoldAmount);
                        }

                        // 触发元宝变化事件
                        if (battleReward.Yuanbao > 0)
                        {
                            var newYuanbaoAmount = (user.yuanbao ?? 0) + battleReward.Yuanbao;
                            // 注意：这里假设有元宝变化事件，如果没有可以注释掉或添加相应的事件方法
                            // await _gameEventTrigger.OnYuanbaoChangedAsync(battleReward.UserId, newYuanbaoAmount);
                        }

                        // 触发用户升级事件
                        if (result.PlayerLeveledUp)
                        {
                            await _gameEventTrigger.OnUserLevelUpAsync(battleReward.UserId, result.NewPlayerLevel);
                        }

                        // 触发经验变化事件（如果需要的话）
                        if (battleReward.Experience > 0)
                        {
                            var newExpAmount = (pet.exp ?? 0) + battleReward.Experience;
                            await _gameEventTrigger.OnExperienceChangedAsync(battleReward.UserId, newExpAmount);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "触发任务事件失败: UserId={UserId}", battleReward.UserId);
                        // 不影响主流程，只记录错误
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    _db.Ado.RollbackTran();
                    return new ClaimRewardResultDTO
                    {
                        Success = false,
                        Message = $"奖励发放失败：{ex.Message}"
                    };
                }
            }
            catch (Exception ex)
            {
                return new ClaimRewardResultDTO
                {
                    Success = false,
                    Message = $"领取奖励失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 计算玩家宠物的最终属性（基础属性+装备+套装+宝石+境界+被动技能等）
        /// 统一的宠物属性计算函数，所有需要宠物属性的地方都应该调用此函数
        /// 紧急修复：集成装备系统到战斗计算
        /// 境界系统迁移：集成境界属性加成
        /// 技能系统统一：集成被动技能属性加成
        /// </summary>
        /// <param name="request">属性计算请求参数</param>
        /// <returns>最终属性结果</returns>
        public async Task<AttributeResultDTO> CalculateFinalAttributesAsync(AttributeRequestDTO request)
        {
            try
            {
                // 1. 获取宠物基础信息
                var pet = _db.Queryable<user_pet>().Where(x => x.user_id == request.UserId && x.id == request.PetId).First();
                if (pet == null)
                    return new AttributeResultDTO();

                // 2. 获取宠物配置信息
                var petConfig = _db.Queryable<pet_config>().Where(x => x.pet_no == pet.pet_no).First();

                // 3. 获取境界信息
                var realmLevel = 0;
                var realmName = "无境界";
                if (!string.IsNullOrEmpty(pet.realm) && int.TryParse(pet.realm, out realmLevel))
                {
                    var realmConfig = _db.Queryable<realm_config>().Where(x => x.realm_level == realmLevel).First();
                    if (realmConfig != null)
                    {
                        realmName = realmConfig.realm_name;
                    }
                }

                // 4. 计算宠物等级
                var petLevel = CalculatePetLevel(pet.exp ?? 0);

                // 5. 初始化基础属性（安全的空值处理）
                var baseResult = new AttributeResultDTO
                {
                    Atk = (int)(pet.atk ?? 0),
                    Def = (int)(pet.def ?? 0),
                    Hit = (int)(pet.hit ?? 0),
                    Dodge = (int)(pet.dodge ?? 0),
                    Spd = (int)(pet.spd ?? 0),
                    Hp = (int)(pet.hp ?? 0),
                    Mp = (int)(pet.mp ?? 0)
                };

                // 6. 计算境界属性加成
                double realmBonus = CalculateRealmAttributeBonus(pet);

                // 7. 应用境界加成到基础属性并填充完整信息
                var result = new AttributeResultDTO
                {
                    // 宠物基本信息
                    PetId = pet.id,
                    PetNo = pet.pet_no,
                    PetName = petConfig?.name ?? "未知宠物",
                    Level = petLevel,
                    Element = petConfig?.attribute ?? "无",
                    RealmLevel = realmLevel,
                    RealmName = realmName,
                    EvolveCount = pet.evolve_count ?? 0,
                    SynthesisCount = pet.synthesis_count ?? 0,
                    NirvanaCount = pet.nirvana_count ?? 0,
                    Exp = pet.exp ?? 0,
                    Growth = pet.growth?.ToString() ?? "0",

                    // 属性值（应用境界加成）
                    Atk = (int)(baseResult.Atk * realmBonus),
                    Def = (int)(baseResult.Def * realmBonus),
                    Hit = (int)(baseResult.Hit * realmBonus),
                    Dodge = (int)(baseResult.Dodge * realmBonus),
                    Spd = (int)(baseResult.Spd * realmBonus),
                    Hp = (int)(baseResult.Hp * realmBonus),
                    Mp = (int)(baseResult.Mp * realmBonus)
                };

                // 5. 计算装备属性加成
                var equipmentAttrs = CalculateEquipmentAttributes(request.PetId);

                // 6. 计算套装属性加成
                var suitAttrs = CalculateSuitAttributes(request.PetId);

                // 7. 计算宝石属性加成
                var gemstoneAttrs = CalculateGemstoneAttributes(request.PetId);

                // 8. 合并所有属性加成 (装备、套装、宝石不受境界影响)
                result.Atk += (int)(equipmentAttrs.GetValueOrDefault("攻击", 0) +
                                   suitAttrs.GetValueOrDefault("攻击", 0) +
                                   gemstoneAttrs.GetValueOrDefault("攻击", 0));

                result.Def += (int)(equipmentAttrs.GetValueOrDefault("防御", 0) +
                                   suitAttrs.GetValueOrDefault("防御", 0) +
                                   gemstoneAttrs.GetValueOrDefault("防御", 0));

                result.Hit += (int)(equipmentAttrs.GetValueOrDefault("命中", 0) +
                                   suitAttrs.GetValueOrDefault("命中", 0) +
                                   gemstoneAttrs.GetValueOrDefault("命中", 0));

                result.Dodge += (int)(equipmentAttrs.GetValueOrDefault("闪避", 0) +
                                     suitAttrs.GetValueOrDefault("闪避", 0) +
                                     gemstoneAttrs.GetValueOrDefault("闪避", 0));

                result.Spd += (int)(equipmentAttrs.GetValueOrDefault("速度", 0) +
                                   suitAttrs.GetValueOrDefault("速度", 0) +
                                   gemstoneAttrs.GetValueOrDefault("速度", 0));

                result.Hp += (int)(equipmentAttrs.GetValueOrDefault("生命", 0) +
                                  suitAttrs.GetValueOrDefault("生命", 0) +
                                  gemstoneAttrs.GetValueOrDefault("生命", 0));

                result.Mp += (int)(equipmentAttrs.GetValueOrDefault("魔法", 0) +
                                  suitAttrs.GetValueOrDefault("魔法", 0) +
                                  gemstoneAttrs.GetValueOrDefault("魔法", 0));

                // 9. 计算扩展属性（加深、抵消、吸血、吸魔）
                result.Deepen = equipmentAttrs.GetValueOrDefault("加深", 0) +
                               suitAttrs.GetValueOrDefault("加深", 0) +
                               gemstoneAttrs.GetValueOrDefault("加深", 0);

                result.Offset = equipmentAttrs.GetValueOrDefault("抵消", 0) +
                               suitAttrs.GetValueOrDefault("抵消", 0) +
                               gemstoneAttrs.GetValueOrDefault("抵消", 0);

                result.Vamp = equipmentAttrs.GetValueOrDefault("吸血", 0) +
                             suitAttrs.GetValueOrDefault("吸血", 0) +
                             gemstoneAttrs.GetValueOrDefault("吸血", 0);

                result.VampMp = equipmentAttrs.GetValueOrDefault("吸魔", 0) +
                               suitAttrs.GetValueOrDefault("吸魔", 0) +
                               gemstoneAttrs.GetValueOrDefault("吸魔", 0);

                // 10. 计算被动技能属性加成（技能系统统一）
                var passiveSkillAttrs = await CalculatePassiveSkillAttributes(request.UserId, pet.id);

                // 11. 应用被动技能效果到基础属性
                result.Atk += (int)passiveSkillAttrs.GetValueOrDefault("攻击", 0);
                result.Def += (int)passiveSkillAttrs.GetValueOrDefault("防御", 0);
                result.Hit += (int)passiveSkillAttrs.GetValueOrDefault("命中", 0);
                result.Dodge += (int)passiveSkillAttrs.GetValueOrDefault("闪避", 0);
                result.Spd += (int)passiveSkillAttrs.GetValueOrDefault("速度", 0);
                result.Hp += (int)passiveSkillAttrs.GetValueOrDefault("生命", 0);
                result.Mp += (int)passiveSkillAttrs.GetValueOrDefault("魔法", 0);

                // 12. 应用被动技能效果到扩展属性
                result.Deepen += passiveSkillAttrs.GetValueOrDefault("加深", 0);
                result.Offset += passiveSkillAttrs.GetValueOrDefault("抵消", 0);
                result.Vamp += passiveSkillAttrs.GetValueOrDefault("吸血", 0);
                result.VampMp += passiveSkillAttrs.GetValueOrDefault("吸魔", 0);

                return result;
            }
            catch (Exception ex)
            {
                // 如果计算失败，返回基础属性，避免战斗系统崩溃
                var pet = _db.Queryable<user_pet>().Where(x => x.user_id == request.UserId && x.id == request.PetId).First();
                if (pet == null)
                    return new AttributeResultDTO();

                var petConfig = _db.Queryable<pet_config>().Where(x => x.pet_no == pet.pet_no).First();
                var petLevel = CalculatePetLevel(pet.exp ?? 0);

                return new AttributeResultDTO
                {
                    // 宠物基本信息
                    PetId = pet.id,
                    PetNo = pet.pet_no,
                    PetName = petConfig?.name ?? "未知宠物",
                    Level = petLevel,
                    Element = petConfig?.attribute ?? "无",
                    RealmLevel = 0,
                    RealmName = "无境界",
                    EvolveCount = pet.evolve_count ?? 0,
                    SynthesisCount = pet.synthesis_count ?? 0,
                    NirvanaCount = pet.nirvana_count ?? 0,
                    Exp = pet.exp ?? 0,
                    Growth = pet.growth?.ToString() ?? "0",

                    // 基础属性值（安全的空值处理）
                    Atk = (int)(pet.atk ?? 0),
                    Def = (int)(pet.def ?? 0),
                    Hit = (int)(pet.hit ?? 0),
                    Dodge = (int)(pet.dodge ?? 0),
                    Spd = (int)(pet.spd ?? 0),
                    Hp = (int)(pet.hp ?? 0),
                    Mp = (int)(pet.mp ?? 0)
                };
            }
        }

        /// <summary>
        /// 计算宠物最终属性（同步版本，向后兼容）
        /// 注意：此版本不包含被动技能加成，建议使用CalculateFinalAttributesAsync
        /// </summary>
        /// <param name="request">属性计算请求参数</param>
        /// <returns>最终属性结果（不含被动技能加成）</returns>
        public AttributeResultDTO CalculateFinalAttributes(AttributeRequestDTO request)
        {
            try
            {
                // 调用异步版本但不等待被动技能计算（为了保持同步）
                var task = CalculateFinalAttributesWithoutPassiveSkills(request);
                return task.Result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算最终属性失败（同步版本），UserId: {UserId}, PetId: {PetId}", request.UserId, request.PetId);

                // 降级到基础属性计算
                var pet = _db.Queryable<user_pet>().Where(x => x.user_id == request.UserId && x.id == request.PetId).First();
                if (pet == null) return new AttributeResultDTO();

                var petConfig = _db.Queryable<pet_config>().Where(x => x.pet_no == pet.pet_no).First();
                var petLevel = CalculatePetLevel(pet.exp ?? 0);

                return new AttributeResultDTO
                {
                    PetId = pet.id,
                    PetNo = pet.pet_no,
                    PetName = petConfig?.name ?? "未知宠物",
                    Level = petLevel,
                    Element = petConfig?.attribute ?? "无",
                    Atk = (int)(pet.atk ?? 0),
                    Def = (int)(pet.def ?? 0),
                    Hit = (int)(pet.hit ?? 0),
                    Dodge = (int)(pet.dodge ?? 0),
                    Spd = (int)(pet.spd ?? 0),
                    Hp = (int)(pet.hp ?? 0),
                    Mp = (int)(pet.mp ?? 0),
                    Growth = pet.growth?.ToString() ?? "0"
                };
            }
        }

        /// <summary>
        /// 计算最终属性（不含被动技能，用于同步调用）
        /// </summary>
        private async Task<AttributeResultDTO> CalculateFinalAttributesWithoutPassiveSkills(AttributeRequestDTO request)
        {
            // 复制原有逻辑但不包含被动技能计算
            var pet = _db.Queryable<user_pet>().Where(x => x.user_id == request.UserId && x.id == request.PetId).First();
            if (pet == null) return new AttributeResultDTO();

            var petConfig = _db.Queryable<pet_config>().Where(x => x.pet_no == pet.pet_no).First();
            var realmLevel = 0;
            var realmName = "无境界";
            if (!string.IsNullOrEmpty(pet.realm) && int.TryParse(pet.realm, out realmLevel))
            {
                var realmConfig = _db.Queryable<realm_config>().Where(x => x.realm_level == realmLevel).First();
                if (realmConfig != null) realmName = realmConfig.realm_name;
            }

            var petLevel = CalculatePetLevel(pet.exp ?? 0);
            var realmBonus = CalculateRealmAttributeBonus(pet);

            var result = new AttributeResultDTO
            {
                PetId = pet.id,
                PetNo = pet.pet_no,
                PetName = petConfig?.name ?? "未知宠物",
                Level = petLevel,
                Element = petConfig?.attribute ?? "无",
                RealmLevel = realmLevel,
                RealmName = realmName,
                EvolveCount = pet.evolve_count ?? 0,
                SynthesisCount = pet.synthesis_count ?? 0,
                NirvanaCount = pet.nirvana_count ?? 0,
                Exp = pet.exp ?? 0,
                Growth = pet.growth?.ToString() ?? "0",
                Atk = (int)((pet.atk ?? 0) * realmBonus),
                Def = (int)((pet.def ?? 0) * realmBonus),
                Hit = (int)((pet.hit ?? 0) * realmBonus),
                Dodge = (int)((pet.dodge ?? 0) * realmBonus),
                Spd = (int)((pet.spd ?? 0) * realmBonus),
                Hp = (int)((pet.hp ?? 0) * realmBonus),
                Mp = (int)((pet.mp ?? 0) * realmBonus)
            };

            // 应用装备、套装、宝石加成
            var equipmentAttrs = CalculateEquipmentAttributes(request.PetId);
            var suitAttrs = CalculateSuitAttributes(request.PetId);
            var gemstoneAttrs = CalculateGemstoneAttributes(request.PetId);

            result.Atk += (int)(equipmentAttrs.GetValueOrDefault("攻击", 0) + suitAttrs.GetValueOrDefault("攻击", 0) + gemstoneAttrs.GetValueOrDefault("攻击", 0));
            result.Def += (int)(equipmentAttrs.GetValueOrDefault("防御", 0) + suitAttrs.GetValueOrDefault("防御", 0) + gemstoneAttrs.GetValueOrDefault("防御", 0));
            result.Hit += (int)(equipmentAttrs.GetValueOrDefault("命中", 0) + suitAttrs.GetValueOrDefault("命中", 0) + gemstoneAttrs.GetValueOrDefault("命中", 0));
            result.Dodge += (int)(equipmentAttrs.GetValueOrDefault("闪避", 0) + suitAttrs.GetValueOrDefault("闪避", 0) + gemstoneAttrs.GetValueOrDefault("闪避", 0));
            result.Spd += (int)(equipmentAttrs.GetValueOrDefault("速度", 0) + suitAttrs.GetValueOrDefault("速度", 0) + gemstoneAttrs.GetValueOrDefault("速度", 0));
            result.Hp += (int)(equipmentAttrs.GetValueOrDefault("生命", 0) + suitAttrs.GetValueOrDefault("生命", 0) + gemstoneAttrs.GetValueOrDefault("生命", 0));
            result.Mp += (int)(equipmentAttrs.GetValueOrDefault("魔法", 0) + suitAttrs.GetValueOrDefault("魔法", 0) + gemstoneAttrs.GetValueOrDefault("魔法", 0));

            result.Deepen = equipmentAttrs.GetValueOrDefault("加深", 0) + suitAttrs.GetValueOrDefault("加深", 0) + gemstoneAttrs.GetValueOrDefault("加深", 0);
            result.Offset = equipmentAttrs.GetValueOrDefault("抵消", 0) + suitAttrs.GetValueOrDefault("抵消", 0) + gemstoneAttrs.GetValueOrDefault("抵消", 0);
            result.Vamp = equipmentAttrs.GetValueOrDefault("吸血", 0) + suitAttrs.GetValueOrDefault("吸血", 0) + gemstoneAttrs.GetValueOrDefault("吸血", 0);
            result.VampMp = equipmentAttrs.GetValueOrDefault("吸魔", 0) + suitAttrs.GetValueOrDefault("吸魔", 0) + gemstoneAttrs.GetValueOrDefault("吸魔", 0);

            return result;
        }

        /// <summary>
        /// 计算装备属性加成  
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>装备属性字典</returns>
        private Dictionary<string, double> CalculateEquipmentAttributes(int petId)
        {
            var result = new Dictionary<string, double>
            {
                {"攻击", 0}, {"命中", 0}, {"防御", 0}, {"速度", 0},
                {"闪避", 0}, {"生命", 0}, {"魔法", 0},
                {"加深", 0}, {"抵消", 0}, {"吸血", 0}, {"吸魔", 0}
            };

            try
            {
                // 获取宠物装备
                var equipments = _db.Queryable<user_equipment>()
                    .Where(x => x.pet_id == petId && x.is_equipped == true)
                    .ToList();

                foreach (var equipment in equipments)
                {
                    // 获取装备详情
                    var equipDetail = _db.Queryable<equipment_detail>()
                        .Where(x => x.equip_id == equipment.equip_id)
                        .First();

                    if (equipDetail == null) continue;

                    // 计算装备基础属性（包含扩展属性）
                    var baseAttributes = new Dictionary<string, double>
                    {
                        {"攻击", ParseAttributeValue(equipDetail.atk?.ToString())},
                        {"防御", ParseAttributeValue(equipDetail.def?.ToString())},
                        {"命中", ParseAttributeValue(equipDetail.hit?.ToString())},
                        {"闪避", ParseAttributeValue(equipDetail.dodge?.ToString())},
                        {"速度", ParseAttributeValue(equipDetail.spd?.ToString())},
                        {"生命", ParseAttributeValue(equipDetail.hp?.ToString())},
                        {"魔法", ParseAttributeValue(equipDetail.mp?.ToString())},
                        {"加深", ParseAttributeValue(equipDetail.deepen?.ToString())},
                        {"抵消", ParseAttributeValue(equipDetail.offset?.ToString())},
                        {"吸血", ParseAttributeValue(equipDetail.vamp?.ToString())},
                        {"吸魔", ParseAttributeValue(equipDetail.vamp_mp?.ToString())}
                    };

                    // 应用强化和五行加成
                    string mainAttr = equipDetail.main_attr ?? "攻击";
                    int strengthenLevel = equipment.strengthen_level ?? 0;
                    string equipElement = equipment.element ?? "无";

                    foreach (var attr in baseAttributes)
                    {
                        if (attr.Value == 0) continue;

                        bool isMainAttribute = attr.Key == mainAttr;
                        int elementBonus = CalculateElementBonus(attr.Key, equipElement);

                        double finalValue = CalculateEquipmentValue(attr.Value.ToString(),
                            strengthenLevel, elementBonus, isMainAttribute);

                        result[attr.Key] += finalValue;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                // 计算失败时返回空属性，避免影响战斗
                return result;
            }
        }

        /// <summary>
        /// 计算套装属性加成  
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>套装属性字典</returns>
        private Dictionary<string, double> CalculateSuitAttributes(int petId)
        {
            var result = new Dictionary<string, double>
            {
                {"攻击", 0}, {"命中", 0}, {"防御", 0}, {"速度", 0},
                {"闪避", 0}, {"生命", 0}, {"魔法", 0},
                {"加深", 0}, {"抵消", 0}, {"吸血", 0}, {"吸魔", 0}
            };

            try
            {
                // 获取宠物装备
                var equipments = _db.Queryable<user_equipment>()
                    .Where(x => x.pet_id == petId && x.is_equipped == true)
                    .ToList();

                // 统计套装件数
                var suitCounts = new Dictionary<string, int>();
                foreach (var equipment in equipments)
                {
                    // 使用user_equipment表中的suit_id字段
                    if (!string.IsNullOrEmpty(equipment.suit_id))
                    {
                        string suitId = equipment.suit_id;
                        suitCounts[suitId] = suitCounts.GetValueOrDefault(suitId, 0) + 1;
                    }
                }

                // 计算激活的套装属性
                foreach (var suitCount in suitCounts)
                {
                    var suitAttributes = _db.Queryable<suit_attribute>()
                        .Where(x => x.suit_id == suitCount.Key)
                        .ToList();

                    foreach (var attr in suitAttributes)
                    {
                        if (suitCount.Value >= attr.piece_count)
                        {
                            string attrType = attr.attribute_type ?? "";
                            double attrValue = ParseAttributeValue(attr.attribute_value);

                            if (result.ContainsKey(attrType))
                            {
                                result[attrType] += attrValue;
                            }
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                return result;
            }
        }

        /// <summary>
        /// 计算宝石属性加成  
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>宝石属性字典</returns>
        private Dictionary<string, double> CalculateGemstoneAttributes(int petId)
        {
            var result = new Dictionary<string, double>
            {
                {"攻击", 0}, {"命中", 0}, {"防御", 0}, {"速度", 0},
                {"闪避", 0}, {"生命", 0}, {"魔法", 0},
                {"加深", 0}, {"抵消", 0}, {"吸血", 0}, {"吸魔", 0}
            };

            try
            {
                // 获取宠物装备
                var equipments = _db.Queryable<user_equipment>()
                    .Where(x => x.pet_id == petId && x.is_equipped == true)
                    .ToList();

                foreach (var equipment in equipments)
                {
                    // 获取装备上的宝石 (使用正确的表名)
                    var gemstones = _db.Queryable<equipment_gemstone>()
                        .Where(x => x.user_equipment_id == equipment.id)
                        .ToList();

                    foreach (var gemstone in gemstones)
                    {
                        var gemstoneDetail = _db.Queryable<gemstone_config>()
                            .Where(x => x.type_name == gemstone.gemstone_type_name)
                            .First();

                        if (gemstoneDetail != null)
                        {
                            string attrType = gemstoneDetail.up_type ?? "";
                            double attrValue = (double)gemstoneDetail.up_num;

                            if (result.ContainsKey(attrType))
                            {
                                result[attrType] += attrValue;
                            }
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                return result;
            }
        }

        /// <summary>
        /// 解析属性值 (支持数值和百分比)
        /// </summary>
        /// <param name="value">属性值字符串</param>
        /// <returns>数值</returns>
        private double ParseAttributeValue(string value)
        {
            if (string.IsNullOrEmpty(value) || value == "0")
                return 0;

            try
            {
                // 移除百分号并转换
                string cleanValue = value.Replace("%", "");
                return Convert.ToDouble(cleanValue);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 计算五行加成类型
        /// </summary>
        /// <param name="attributeType">属性类型</param>
        /// <param name="equipElement">装备五行</param>
        /// <returns>加成类型 (0=无加成, 1=相克减成, 2=相生加成)</returns>
        private int CalculateElementBonus(string attributeType, string equipElement)
        {
            if (string.IsNullOrEmpty(equipElement) || equipElement == "无")
                return 0;

            // 简化的五行相生相克关系
            var elementGeneration = new Dictionary<string, string>
            {
                {"攻击", "火"}, {"防御", "土"}, {"生命", "水"},
                {"魔法", "木"}, {"速度", "金"}, {"命中", "火"}, {"闪避", "风"}
            };

            var elementRestraint = new Dictionary<string, string>
            {
                {"攻击", "水"}, {"防御", "木"}, {"生命", "土"},
                {"魔法", "金"}, {"速度", "火"}, {"命中", "水"}, {"闪避", "土"}
            };

            if (elementGeneration.GetValueOrDefault(attributeType, "") == equipElement)
                return 2; // 相生加成
            else if (elementRestraint.GetValueOrDefault(attributeType, "") == equipElement)
                return 1; // 相克减成
            else
                return 0; // 无加成
        }

        /// <summary>
        /// 计算装备属性值 
        /// </summary>
        /// <param name="baseValue">基础值</param>
        /// <param name="strengthenLevel">强化等级</param>
        /// <param name="elementBonus">五行加成类型</param>
        /// <param name="isMainAttribute">是否主属性</param>
        /// <returns>最终属性值</returns>
        private double CalculateEquipmentValue(string baseValue, int strengthenLevel,
            int elementBonus, bool isMainAttribute)
        {
            if (string.IsNullOrEmpty(baseValue) || baseValue == "0")
                return 0;

            double value = Convert.ToDouble(baseValue);

            // 只有主属性才受强化等级和五行影响
            if (!isMainAttribute)
                return value;

            // 根据五行加成类型计算 
            if (baseValue.Contains(".")) // 百分比型
            {
                return CalculateEquipmentPercentage(value, strengthenLevel, elementBonus);
            }
            else // 数值型
            {
                return CalculateEquipmentNumber(value, strengthenLevel, elementBonus);
            }
        }

        /// <summary>
        /// 计算装备数值型属性
        /// </summary>
        private double CalculateEquipmentNumber(double baseValue, int level, int elementType)
        {
            double multiplier = 1.0;

            if (elementType == 0) // 五行相同
            {
                if (level >= 0 && level <= 10)
                {
                    multiplier = 1.0 + 0.05 * level;
                }
                else
                {
                    multiplier = 0.5 + 0.1 * level;
                }
            }
            else if (elementType == 1) // 五行相克
            {
                if (level >= 0 && level <= 10)
                {
                    multiplier = 0.85 + 0.05 * level;
                }
                else
                {
                    multiplier = 0.35 + 0.1 * level;
                }
            }
            else // 五行相生
            {
                if (level >= 0 && level <= 10)
                {
                    multiplier = 1.15 + 0.05 * level;
                }
                else
                {
                    multiplier = 0.65 + 0.1 * level;
                }
            }

            return baseValue * multiplier;
        }

        /// <summary>
        /// 计算装备百分比型属性
        /// </summary>
        private double CalculateEquipmentPercentage(double baseValue, int level, int elementType)
        {
            double multiplier = 1.0;

            if (elementType == 0) // 五行相同
            {
                if (level >= 0 && level <= 10)
                {
                    multiplier = 1.0 + 0.02 * level;
                }
                else
                {
                    multiplier = 0.2 + 0.04 * level;
                }
            }
            else if (elementType == 1) // 五行相克
            {
                if (level >= 0 && level <= 10)
                {
                    multiplier = 0.85 + 0.02 * level;
                }
                else
                {
                    multiplier = 0.05 + 0.04 * level;
                }
            }
            else // 五行相生
            {
                if (level >= 0 && level <= 10)
                {
                    multiplier = 1.15 + 0.02 * level;
                }
                else
                {
                    multiplier = 0.35 + 0.04 * level;
                }
            }

            return baseValue * multiplier;
        }

        /// <summary>
        /// 计算境界属性加成
        /// </summary>
        /// <param name="pet">宠物信息</param>
        /// <returns>境界属性加成倍率</returns>
        private double CalculateRealmAttributeBonus(user_pet pet)
        {
            try
            {
                // 如果没有境界信息，返回无加成
                if (string.IsNullOrEmpty(pet.realm))
                    return 1.0;

                // 解析境界等级
                if (!int.TryParse(pet.realm, out int realmLevel))
                    return 1.0;

                // 境界算法
                double stateBuffer = 1.0;

                if (realmLevel < 16)
                {
                    // 16级以下，每级增加1%的属性
                    stateBuffer = 1.0 + (double)realmLevel / 100.0;
                }
                else if (realmLevel >= 16)
                {
                    // 16级以上，基础16% + 每级额外3%的属性
                    stateBuffer = 1.0 + 0.16 + ((double)(realmLevel - 15) / 100.0) * 3;
                }

                return stateBuffer;
            }
            catch (Exception ex)
            {
                // 计算失败时返回无加成
                return 1.0;
            }
        }

        /// <summary>
        /// 回合制战斗计算
        /// </summary>
        /// <param name="request">战斗请求参数</param>
        /// <returns>战斗结果</returns>
        /// <summary>
        /// 战斗计算 - 一次性计算整个战斗过程
        /// </summary>
        public async Task<BattleResultDTO> BattleCalculate(BattleRequestDTO request)
        {
            var startTime = DateTime.Now;
            var random = new Random();

            // 1. 基础验证
            if (!_antiCheatService.ValidateRequestFrequency(request.UserId))
            {
                return new BattleResultDTO
                {
                    IsBattleEnd = true,
                    IsWin = false,
                    Message = "请求频率过高，请稍后再试",
                    AutoBattleStatus = 10 // 作弊标记
                };
            }

            // 2. 获取地图和怪物信息
            var map = _db.Queryable<map_config>().Where(x => x.map_id == request.MapId).First();
            if (map == null)
                return new BattleResultDTO { IsBattleEnd = true, IsWin = false, Message = "地图不存在" };

            var monsters = _db.Queryable<map_monster>().Where(x => x.map_id == request.MapId).ToList();
            if (monsters == null || monsters.Count == 0)
                return new BattleResultDTO { IsBattleEnd = true, IsWin = false, Message = "该地图无怪物" };

            // 根据传入的怪物ID选择怪物，如果没有传入则随机选择
            map_monster monster;
            
            if (request.MonsterId.HasValue)
            {
                // 使用指定的怪物ID
                monster = monsters.FirstOrDefault(m => m.monster_id == request.MonsterId.Value);
                if (monster == null)
                {
                    return new BattleResultDTO { IsBattleEnd = true, IsWin = false, Message = $"指定的怪物ID {request.MonsterId.Value} 在该地图不存在" };
                }
                Console.WriteLine($"[战斗] 用户 {request.UserId} 选择战斗怪物ID: {request.MonsterId.Value}");
            }
            else
            {
                return new BattleResultDTO { IsBattleEnd = true, IsWin = false, Message = $"指定的怪物在该地图不存在" };
            }

            // 3. 获取用户信息
            var user = _db.Queryable<user>().Where(x => x.id == request.UserId).First();
            if (user == null)
                return new BattleResultDTO { IsBattleEnd = true, IsWin = false, Message = "用户不存在" };

            // 4. 创建属性对象
            var monsterAttr = new AttributeResultDTO
            {
                Atk = (int)(monster.atk ?? 0),
                Def = (int)(monster.def ?? 0),
                Hit = 85,
                Dodge = (int)(monster.dodge ?? 0),
                Spd = (int)(monster.spd ?? 0),
                Hp = (int)(monster.hp ?? 0),
                Mp = (int)(monster.mp ?? 0),
                Growth = monster.growth?.ToString() ?? "0"
            };

            // 使用异步版本获取包含被动技能加成的最终属性
            var playerAttr = await CalculateFinalAttributesAsync(new AttributeRequestDTO { UserId = request.UserId, PetId = request.PetId });

            // 获取玩家宠物信息用于等级计算
            var playerPet = _db.Queryable<user_pet>().Where(x => x.user_id == request.UserId && x.id == request.PetId).First();
            if (playerPet == null)
                return new BattleResultDTO { IsBattleEnd = true, IsWin = false, Message = "宠物不存在" };

            // 5. 初始化战斗状态
            long playerCurrentHp = playerAttr.Hp;
            long monsterCurrentHp = monsterAttr.Hp;
            long playerCurrentMp = playerAttr.Mp;
            long monsterCurrentMp = monsterAttr.Mp;
            int currentRound = 0;

            // 6. 先手判断
            double playerSpeed = random.Next(97, 103) * playerAttr.Spd / 100.0;
            double monsterSpeed = random.Next(97, 103) * monsterAttr.Spd / 100.0;
            bool isFirstStrike = playerSpeed > monsterSpeed;
            bool isPlayerTurn = isFirstStrike;

            var battleRounds = new List<BattleRoundDTO>();
            bool battleEnd = false;
            bool playerWin = false;

            // 7. 统计数据
            long totalDamageDealt = 0;
            long totalDamageReceived = 0;
            long totalDamageAmplified = 0;
            long totalDamageReduced = 0;
            long totalLifeSteal = 0;
            long totalManaSteal = 0;

            // 8. 执行战斗循环
            while (!battleEnd && currentRound < 50) // 增加最大回合数限制
            {
                currentRound++;
                var roundResult = new BattleRoundDTO { Round = currentRound };

                if (isPlayerTurn)
                {
                    // 获取玩家和怪物等级
                    int playerLevel = await _levelService.CalculateLevelAsync(playerPet.exp ?? 0, "pet");
                    int monsterLevel = GetMonsterLevel(monster);

                    // 计算主动技能倍数和魔法消耗（被动技能效果已在playerAttr中）
                    double skillMultiplier = await CalculateActiveSkillMultiplier(playerPet.id, request.SkillId);
                    int manaCost = await CalculateActiveSkillManaCost(playerPet.id, request.SkillId);
                    bool hasEnoughMana = (playerPet.current_mp ?? 0) >= manaCost;

                    // 如果魔法不足，降级为普通攻击
                    if (!string.IsNullOrEmpty(request.SkillId) && !hasEnoughMana)
                    {
                        _logger.LogWarning("宠物魔法不足，使用普通攻击。PetId: {PetId}, SkillId: {SkillId}, CurrentMana: {CurrentMana}, RequiredMana: {RequiredMana}",
                            playerPet.id, request.SkillId, playerPet.current_mp ?? 0, manaCost);
                        skillMultiplier = 1.0;
                        manaCost = 0;
                        hasEnoughMana = true;
                    }

                    // 使用增强版伤害计算（playerAttr已包含被动技能效果）
                    long baseDamage = OriginalBattleAdapter.CalculateEnhancedPlayerDamage(
                        playerAttr, monsterAttr, playerLevel, monsterLevel, skillMultiplier);

                    // 扣除魔法值（如果使用了技能且魔法足够）
                    if (!string.IsNullOrEmpty(request.SkillId) && hasEnoughMana && manaCost > 0)
                    {
                        playerPet.current_mp = Math.Max(0, (playerPet.current_mp ?? 0) - manaCost);
                        // 更新数据库中的魔法值
                        await _db.Updateable<user_pet>()
                            .SetColumns(up => up.current_mp == playerPet.current_mp)
                            .Where(up => up.id == playerPet.id)
                            .ExecuteCommandAsync();
                    }

                    // 应用成长值反作弊检查
                    double playerGrowth = Convert.ToDouble(playerAttr.Growth ?? "0");
                    double monsterGrowth = Convert.ToDouble(monsterAttr.Growth ?? "0");
                    baseDamage = OriginalBattleAdapter.ApplyGrowthAntiCheat(playerGrowth, monsterGrowth, baseDamage);

                    // 应用地图特殊规则
                    baseDamage = MapSpecialRules.ApplyMapDamageRules(baseDamage, request.MapId.ToString(), user, monster);

                    // 反作弊验证
                    if (!_antiCheatService.ValidateDamageValue(request.UserId, baseDamage, playerAttr.Atk, monsterAttr.Def))
                    {
                        return new BattleResultDTO
                        {
                            IsBattleEnd = true,
                            IsWin = false,
                            Message = "检测到异常数据",
                            AutoBattleStatus = 10
                        };
                    }

                    // 计算加深伤害
                    double amplifyRate = GetDamageAmplifyRate(playerAttr);
                    long amplifiedDamage = OriginalBattleAdapter.CalculateAmplifiedDamage(baseDamage, amplifyRate);
                    if (amplifiedDamage > 0) totalDamageAmplified += amplifiedDamage;

                    long totalPlayerDamage = baseDamage + Math.Max(0, amplifiedDamage);

                    // 计算吸血吸魔
                    double lifeStealRate = GetLifeStealRate(playerAttr);
                    double manaStealRate = GetManaStealRate(playerAttr);

                    // 应用地图特效限制
                    var (adjustedLifeSteal, _) = MapSpecialRules.ApplyMapEffectLimits(request.MapId.ToString(), lifeStealRate, 0);

                    long lifeSteal = OriginalBattleAdapter.CalculateLifeSteal(totalPlayerDamage, adjustedLifeSteal, false, request.MapId.ToString());
                    long manaSteal = OriginalBattleAdapter.CalculateManaSteal(totalPlayerDamage, manaStealRate);

                    if (lifeSteal > 0) totalLifeSteal += lifeSteal;
                    if (manaSteal > 0) totalManaSteal += manaSteal;

                    // 应用伤害和回复
                    monsterCurrentHp = Math.Max(0, monsterCurrentHp - totalPlayerDamage);
                    playerCurrentHp = Math.Min(playerAttr.Hp, playerCurrentHp + lifeSteal);
                    playerCurrentMp = Math.Min(playerAttr.Mp, playerCurrentMp + manaSteal);

                    // 记录回合信息
                    roundResult.AttackerType = "Player";
                    roundResult.Damage = (int)totalPlayerDamage;
                    roundResult.IsCritical = amplifiedDamage > 0;
                    roundResult.IsHit = totalPlayerDamage > 0;
                    roundResult.SkillId = request.SkillId;
                    roundResult.PlayerHpAfter = (int)playerCurrentHp;
                    roundResult.MonsterHpAfter = (int)monsterCurrentHp;
                    roundResult.Description = amplifiedDamage > 0 ?
                        $"暴击！对怪物造成了{totalPlayerDamage}点伤害！" :
                        $"对怪物造成了{totalPlayerDamage}点伤害！";

                    totalDamageDealt += totalPlayerDamage;

                    // 检查怪物是否死亡
                    if (monsterCurrentHp <= 0)
                    {
                        battleEnd = true;
                        playerWin = true;
                    }
                }
                else
                {
                    // 怪物攻击 (使用增强版伤害计算，考虑等级差异)
                    int playerLevel = await _levelService.CalculateLevelAsync(playerPet.exp ?? 0, "pet");
                    int monsterLevel = GetMonsterLevel(monster);

                    long baseDamage = OriginalBattleAdapter.CalculateEnhancedMonsterDamage(
                        monsterAttr, playerAttr, monsterLevel, playerLevel);

                    // 计算抵消伤害
                    double reductionRate = GetDamageReductionRate(playerAttr);
                    var (_, adjustedReduction) = MapSpecialRules.ApplyMapEffectLimits(request.MapId.ToString(), 0, reductionRate);

                    long reducedDamage = OriginalBattleAdapter.CalculateReducedDamage(baseDamage, adjustedReduction, false, request.MapId.ToString());
                    if (reducedDamage > 0) totalDamageReduced += reducedDamage;

                    long finalDamage = Math.Max(1, baseDamage - Math.Max(0, reducedDamage));

                    playerCurrentHp = Math.Max(0, playerCurrentHp - finalDamage);

                    // 记录回合信息
                    roundResult.AttackerType = "Monster";
                    roundResult.Damage = (int)finalDamage;
                    roundResult.IsCritical = false;
                    roundResult.IsHit = true;
                    roundResult.PlayerHpAfter = (int)playerCurrentHp;
                    roundResult.MonsterHpAfter = (int)monsterCurrentHp;
                    roundResult.Description = $"怪物对你造成了{finalDamage}点伤害！";

                    totalDamageReceived += finalDamage;

                    // 检查玩家是否死亡
                    if (playerCurrentHp <= 0)
                    {
                        battleEnd = true;
                        playerWin = false;
                    }
                }

                battleRounds.Add(roundResult);
                isPlayerTurn = !isPlayerTurn; // 切换回合
            }

            // 9. 战斗时间验证
            var endTime = DateTime.Now;
            var battleDuration = (long)(endTime - startTime).TotalMilliseconds;

            //if (!_antiCheatService.ValidateBattleTiming(request.UserId, battleDuration, currentRound))
            //{
            //    return new BattleResultDTO
            //    {
            //        IsBattleEnd = true,
            //        IsWin = false,
            //        Message = "你很喜欢搞事咯?",
            //        AutoBattleStatus = 10
            //    };
            //}

            // 10. 创建战斗结果
            var result = new BattleResultDTO
            {
                // 基础信息
                IsBattleEnd = true,
                IsWin = playerWin,
                CurrentRound = currentRound,
                PlayerCurrentHp = (int)playerCurrentHp,
                MonsterCurrentHp = (int)monsterCurrentHp,
                PlayerMaxHp = playerAttr.Hp,
                MonsterMaxHp = monsterAttr.Hp,
                BattleRounds = battleRounds,


                BattleEndFlag = 1, // 1为结束
                IsDead = playerWin ? 0 : 1, // 1为死亡 0为存活
                TotalDamageDealt = totalDamageDealt,
                TotalDamageReceived = totalDamageReceived,
                MonsterRemainingHp = monsterCurrentHp,
                PlayerRemainingHp = playerCurrentHp,
                DamageAmplified = totalDamageAmplified,
                DamageReduced = totalDamageReduced,
                LifeSteal = totalLifeSteal,
                ManaSteal = totalManaSteal,
                RemainingMp = playerCurrentMp,
                IsFirstStrike = isFirstStrike ? 1 : 0,
                AutoBattleStatus = 0 // 0正常
            };

            // 11. 计算奖励信息 (仅胜利时，存储到临时字典)
            if (playerWin)
            {
                // 基础经验
                int baseExp = monster.exp.HasValue ? (int)monster.exp.Value : 0;

                // 应用地图经验倍率
                double expMultiplier = MapSpecialRules.GetMapExpMultiplier(request.MapId.ToString());
                result.Exp = (int)(baseExp * expMultiplier);
                result.ExperienceGained = result.Exp;

                // 计算金币和元宝
                double goldMultiplier = MapSpecialRules.GetMapGoldMultiplier(request.MapId.ToString());
                double yuanbaoMultiplier = MapSpecialRules.GetMapYuanbaoMultiplier(request.MapId.ToString());

                result.GoldGained = (int)(random.Next(50, 200) * goldMultiplier);
                result.YuanbaoGained = (int)(random.Next(0, 10) * yuanbaoMultiplier);

                // 计算掉落
                var dropItems = CalculateDropItems(request.MapId, monster.monster_id ?? 0, user);
                result.DropItems = dropItems;
                result.ItemsGainedString = string.Join(",", dropItems);

                // 生成战斗唯一标识
                string battleId = $"{request.UserId}_{request.PetId}_{DateTime.Now.Ticks}";
                result.BattleId = battleId;



                // 存储奖励信息到临时字典（安全机制）
                var battleReward = new BattleReward
                {
                    UserId = request.UserId,
                    PetId = request.PetId,
                    MapId = request.MapId,
                    MonsterId = monster.monster_id ?? 0,
                    IsWin = true,
                    Experience = result.ExperienceGained,
                    Gold = result.GoldGained,
                    Yuanbao = result.YuanbaoGained,
                    DropItems = dropItems,
                    BattleId = battleId,
                    CreateTime = DateTime.Now,

                };

                lock (_rewardLock)
                {
                    _battleRewards[battleId] = battleReward;

                    // 清理过期的奖励记录（超过10分钟）
                    var expiredKeys = _battleRewards
                        .Where(x => DateTime.Now - x.Value.CreateTime > TimeSpan.FromMinutes(10))
                        .Select(x => x.Key)
                        .ToList();

                    foreach (var key in expiredKeys)
                    {
                        _battleRewards.Remove(key);
                    }
                }

                result.Message = $"战斗胜利！可获得{result.Exp}经验值，{result.GoldGained}金币，{result.YuanbaoGained}元宝！请使用BattleId: {battleId} 调用ClaimBattleReward领取奖励。";
            }
            else
            {
                result.Message = "战斗失败！";
            }

            return result;
        }

        /// <summary>
        /// 获取技能倍数
        /// </summary>
        /// <param name="skillId">技能ID</param>
        /// <returns>技能倍数</returns>
        private double GetSkillMultiplier(string skillId)
        {
            if (string.IsNullOrEmpty(skillId))
                return 1.0;

            // 这里可以从数据库查询技能配置
            // 暂时返回固定值
            return skillId switch
            {
                "skill_001" => 1.2,
                "skill_002" => 1.5,
                "skill_003" => 2.0,
                _ => 1.0
            };
        }

        /// <summary>
        /// 获取伤害加深率 - 统一使用CalculateFinalAttributes计算的结果
        /// </summary>
        /// <param name="playerAttr">玩家属性</param>
        /// <returns>加深率</returns>
        private double GetDamageAmplifyRate(AttributeResultDTO playerAttr)
        {
            // 直接使用CalculateFinalAttributes计算的加深值
            return playerAttr.Deepen;
        }

        /// <summary>
        /// 获取伤害抵消率 - 统一使用CalculateFinalAttributes计算的结果
        /// </summary>
        /// <param name="playerAttr">玩家属性</param>
        /// <returns>抵消率</returns>
        private double GetDamageReductionRate(AttributeResultDTO playerAttr)
        {
            // 直接使用CalculateFinalAttributes计算的抵消值
            return playerAttr.Offset;
        }

        /// <summary>
        /// 获取吸血率 - 统一使用CalculateFinalAttributes计算的结果
        /// </summary>
        /// <param name="playerAttr">玩家属性</param>
        /// <returns>吸血率</returns>
        private double GetLifeStealRate(AttributeResultDTO playerAttr)
        {
            // 直接使用CalculateFinalAttributes计算的吸血值
            return playerAttr.Vamp;
        }

        /// <summary>
        /// 获取吸魔率 - 统一使用CalculateFinalAttributes计算的结果
        /// </summary>
        /// <param name="playerAttr">玩家属性</param>
        /// <returns>吸魔率</returns>
        private double GetManaStealRate(AttributeResultDTO playerAttr)
        {
            // 直接使用CalculateFinalAttributes计算的吸魔值
            return playerAttr.VampMp;
        }

        /// <summary>
        /// 计算道具掉落
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="monsterId">怪物ID</param>
        /// <param name="user">用户信息</param>
        /// <returns>掉落物品列表</returns>
        private List<string> CalculateDropItems(int mapId, int monsterId, user user)
        {
            var dropItems = new List<string>();
            var random = new Random();

            try
            {
                // 获取掉落配置
                var drops = _db.Queryable<drop_config>()
                    .Where(x => x.map_id == mapId &&
                           (x.drop_type == "地图掉落" || (x.drop_type == "怪物掉落" && x.monster_id == monsterId)))
                    .ToList();

                foreach (var drop in drops)
                {
                    // 优先使用 drop_items_json 字段
                    if (!string.IsNullOrEmpty(drop.drop_items_json))
                    {
                        try
                        {
                            // 解析 JSON 格式的掉落配置
                            var dropConfigs = Newtonsoft.Json.JsonConvert.DeserializeObject<List<DropItemConfig>>(drop.drop_items_json);

                            if (dropConfigs != null && dropConfigs.Count > 0)
                            {
                                foreach (var config in dropConfigs)
                                {
                                    if (string.IsNullOrEmpty(config.ItemId)) continue;

                                    double dropRate = config.DropRate;

                                    // 应用VIP掉落加成
                                    if (user.vip_level.HasValue && user.vip_level.Value >= 1)
                                    {
                                        dropRate *= (1 + user.vip_level.Value * 0.1); // VIP每级增加10%掉落率
                                    }

                                    // 判断是否掉落
                                    if (dropRate >= 1.0 || random.NextDouble() < dropRate)
                                    {
                                        int minCount = Math.Max(1, config.MinCount);
                                        int maxCount = Math.Max(minCount, config.MaxCount);
                                        int count = random.Next(minCount, maxCount + 1);
                                        dropItems.Add($"{config.ItemId}*{count}");

                                        _logger.LogDebug("掉落物品: {ItemId}*{Count}, 掉落率: {DropRate}",
                                            config.ItemId, count, dropRate);
                                    }
                                }
                                continue; // 使用了 JSON 配置，跳过旧格式处理
                            }
                        }
                        catch (Exception jsonEx)
                        {
                            _logger.LogError(jsonEx, "解析掉落配置JSON失败: {Json}", drop.drop_items_json);
                            // JSON 解析失败，继续使用旧格式
                        }
                    }

                    // 回退到旧格式（单个物品配置）
                    if (!string.IsNullOrEmpty(drop.item_id))
                    {
                        double dropRate = drop.drop_rate.HasValue ? (double)drop.drop_rate.Value : 1.0;

                        // 应用VIP掉落加成
                        if (user.vip_level.HasValue && user.vip_level.Value >= 1)
                        {
                            dropRate *= (1 + user.vip_level.Value * 0.1); // VIP每级增加10%掉落率
                        }

                        if (dropRate >= 1.0 || random.NextDouble() < dropRate)
                        {
                            int count = random.Next(drop.min_count ?? 1, (drop.max_count ?? 1) + 1);
                            dropItems.Add($"{drop.item_id}*{count}");

                            _logger.LogDebug("掉落物品(旧格式): {ItemId}*{Count}, 掉落率: {DropRate}",
                                drop.item_id, count, dropRate);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 掉落计算失败不影响战斗结果
                _logger.LogError(ex, "计算掉落失败: MapId={MapId}, MonsterId={MonsterId}", mapId, monsterId);
            }

            _logger.LogDebug("掉落计算完成: MapId={MapId}, MonsterId={MonsterId}, 掉落物品数量={Count}",
                mapId, monsterId, dropItems.Count);

            return dropItems;
        }

        /// <summary>
        /// 计算伤害
        /// </summary>
        /// <param name="attacker">攻击者属性</param>
        /// <param name="defender">防御者属性</param>
        /// <param name="skillId">技能ID</param>
        /// <param name="random">随机数生成器</param>
        /// <returns>伤害值、是否暴击、是否命中</returns>
        private (int damage, bool isCritical, bool isHit) CalculateDamage(
            AttributeResultDTO attacker, AttributeResultDTO defender, string skillId, Random random)
        {
            // 命中计算
            bool isHit = true;
            if (attacker.Hit < defender.Dodge)
            {
                var hitChance = Math.Max(0.1, 1.0 - (double)(defender.Dodge - attacker.Hit) / Math.Max(defender.Dodge, 1) * 0.5);
                isHit = random.NextDouble() < hitChance;
            }

            if (!isHit)
                return (0, false, false);

            // 暴击计算（10%基础暴击率）
            bool isCritical = random.NextDouble() < 0.1;

            // 基础伤害计算
            var baseDamage = Math.Max(attacker.Atk - defender.Def * 0.7m, 1);

            // 技能加成（简化处理）
            decimal skillMultiplier = 1.0m;
            if (!string.IsNullOrEmpty(skillId))
            {
                skillMultiplier = 1.2m; // 技能增加20%伤害
            }

            // 暴击加成
            if (isCritical)
                skillMultiplier *= 1.5m;

            // 随机伤害浮动（90%-110%）
            var randomMultiplier = (decimal)(0.9 + random.NextDouble() * 0.2);

            var finalDamage = baseDamage * skillMultiplier * randomMultiplier;
            return ((int)Math.Round(finalDamage), isCritical, isHit);
        }

        #region 宠物核心系统实现

        /// <summary>
        /// 获取主战宠物信息 - 专为Battle.html设计
        /// 返回Battle.html期望的JSON格式数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>主战宠物信息</returns>
        public async Task<BattlePetInfoResultDTO> GetMainPet(int userId)
        {
            try
            {
                // 1. 获取用户信息
                var user = _db.Queryable<user>().Where(x => x.id == userId).First();
                if (user == null)
                {
                    return new BattlePetInfoResultDTO
                    {
                        Success = false,
                        Message = "用户不存在"
                    };
                }

                // 2. 获取主战宠物
                var mainPet = _db.Queryable<user_pet>()
                    .Where(x => x.user_id == userId && x.is_main == true && x.status != "丢弃")
                    .First();

                if (mainPet == null)
                {
                    return new BattlePetInfoResultDTO
                    {
                        Success = false,
                        Message = "未找到主战宠物"
                    };
                }

                // 3. 获取宠物配置信息
                var petConfig = _db.Queryable<pet_config>()
                    .Where(x => x.pet_no == mainPet.pet_no)
                    .First();

                // 4. 获取宠物技能
                var petSkills = _db.Queryable<user_pet_skill>()
                    .Where(x => x.user_pet_id == mainPet.id)
                    .ToList();

                // 5. 格式化技能显示字符串
                var skillDisplay = FormatSkillDisplayForBattle(petSkills);

                // 6. 使用统一的属性计算函数获取最终属性
                var finalAttributes = await CalculateFinalAttributesAsync(new AttributeRequestDTO
                {
                    UserId = userId,
                    PetId = mainPet.id
                });

                // 7. 计算升级经验（使用等级服务）
                var currentLevel = CalculatePetLevel(mainPet.exp ?? 0);
                var upgradeExp = await _levelService.GetUpgradeExpAsync(currentLevel+1, "pet");

                // 8. 构建Battle.html期望的数据格式（使用统一计算的属性）
                var battlePetInfo = new BattlePetInfoDTO
                {
                    宠物id = mainPet.id,
                    形象 = mainPet.image ?? mainPet.pet_no,
                    指定形象 = mainPet.image,
                    生命 = finalAttributes.Hp,
                    最大生命 = finalAttributes.Hp,  // 使用统一计算的最大生命值
                    魔法 = mainPet.mp ?? 0,
                    最大魔法 = finalAttributes.Mp,  // 使用统一计算的最大魔法值
                    等级 = currentLevel,
                    宠物名字 = mainPet.custom_name ?? mainPet.name ?? petConfig?.name ?? "未命名宠物",
                    五行 =petConfig?.attribute ?? "无",
                    当前经验 = mainPet.exp ?? 0,
                    升级经验 = upgradeExp,  // 使用等级服务计算的升级经验
                    技能显示 = skillDisplay,
                    攻击 = finalAttributes.Atk,     // 使用统一计算的攻击力
                    防御 = finalAttributes.Def,     // 使用统一计算的防御力
                    速度 = finalAttributes.Spd,     // 使用统一计算的速度
                    闪避 = finalAttributes.Dodge,   // 使用统一计算的闪避
                    成长 = mainPet.growth ?? 1.0m,
                    境界 = mainPet.realm ?? "凡境",
                    命中 = finalAttributes.Hit,     // 使用统一计算的命中
                    加深伤害 = finalAttributes.Deepen,
                    抵消伤害 = finalAttributes.Offset,
                    吸血比例 = finalAttributes.Vamp,
                    吸魔比例 = finalAttributes.VampMp
                };

                return new BattlePetInfoResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    PetInfo = battlePetInfo,
                    // 兼容原有格式
                    Pets = new List<PetInfoDTO>
                    {
                        new PetInfoDTO
                        {
                            Id = mainPet.id,
                            PetNo = mainPet.pet_no,
                            Name = battlePetInfo.宠物名字,
                            Element = battlePetInfo.五行,
                            Level = battlePetInfo.等级,
                            Exp = battlePetInfo.当前经验,
                            Hp = battlePetInfo.生命,
                            Mp = battlePetInfo.魔法,
                            Growth = battlePetInfo.成长,
                            Realm = battlePetInfo.境界,
                            IsMainPet = true,
                            Status = mainPet.status,
                            IsMain = true
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                return new BattlePetInfoResultDTO
                {
                    Success = false,
                    Message = $"获取主战宠物信息失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取用户宠物列表
        /// </summary>
        /// <param name="request">宠物列表查询请求</param>
        /// <returns>宠物列表结果</returns>
        public WebApplication_HM.DTOs.ResultDTO.PetListResultDTO GetUserPets(WebApplication_HM.DTOs.RequestDTO.PetListRequestDTO request)
        {
            try
            {
                // 查询用户信息获取主宠物ID
                var user = _db.Queryable<user>().Where(x => x.id == request.UserId).First();
                if (user == null)
                {
                    return new PetListResultDTO
                    {
                        Success = false,
                        Message = "用户不存在"
                    };
                }

                // 构建查询条件
                var query = _db.Queryable<user_pet>()
                    .Where(x => x.user_id == request.UserId)
                    .Where(x => x.status != "牧场")
                    .Where(x => x.status != "丢弃"); // 只排除丢弃状态的宠物

                // 🔧 修改：根据状态过滤
                if (!string.IsNullOrEmpty(request.Status))
                {
                    // 验证状态值是否有效
                    if (request.Status != "牧场" && request.Status != "携带")
                    {
                        return new PetListResultDTO
                        {
                            Success = false,
                            Message = "无效的宠物状态，只支持：牧场、携带"
                        };
                    }
                    query = query.Where(x => x.status == request.Status);
                }

                // 🔧 修改：根据是否主战宠物过滤
                if (request.OnlyMainPet.HasValue && request.OnlyMainPet.Value)
                {
                    query = query.Where(x => x.is_main == true);
                }

                // 执行查询并排序
                var userPets = query.OrderBy(x => x.pet_no).ToList();

                // 查询宠物配置信息
                var petConfigs = _db.Queryable<pet_config>().ToList();

                var petList = userPets.Select(pet =>
                {
                    var config = petConfigs.FirstOrDefault(c => c.pet_no == pet.pet_no);
                    return new PetInfoDTO
                    {
                        Id = pet.id,
                        PetNo = pet.pet_no,
                        Name = config?.name ?? "未知宠物",
                        Element =config?.attribute ?? "无",
                        Level = CalculatePetLevel(pet.exp ?? 0), // 根据经验计算等级
                        Exp = pet.exp ?? 0,
                        Hp = pet.hp ?? 0,
                        Mp = pet.mp ?? 0,
                        Growth = pet.growth ?? 0,
                        Realm = pet.realm ?? "无境界",
                        IsMainPet = user.main_pet_id == pet.id,
                        Status = pet.status, // 🔧 新增：返回宠物状态
                        IsMain = pet.is_main // 🔧 新增：返回是否主战宠物标识
                    };
                }).ToList();

                // 🔧 新增：统计信息
                var statusCounts = _db.Queryable<user_pet>()
                    .Where(x => x.user_id == request.UserId && x.status != "丢弃")
                    .GroupBy(x => x.status)
                    .Select(g => new { Status = g.status, Count = SqlFunc.AggregateCount(g.status) })
                    .ToList();

                var totalCount = userPets.Count;
                var ranchCount = statusCounts.FirstOrDefault(x => x.Status == "牧场")?.Count ?? 0;
                var carryCount = statusCounts.FirstOrDefault(x => x.Status == "携带")?.Count ?? 0;

                return new PetListResultDTO
                {
                    Success = true,
                    Message = $"获取成功，共查询到{totalCount}只宠物" + 
                             (string.IsNullOrEmpty(request.Status) ? $"（牧场:{ranchCount}只，携带:{carryCount}只）" : 
                              $"（{request.Status}状态）"),
                    Pets = petList,
                    MainPetId = user.main_pet_id,
                    TotalCount = totalCount, // 🔧 新增：总数量
                    RanchCount = ranchCount, // 🔧 新增：牧场数量
                    CarryCount = carryCount  // 🔧 新增：携带数量
                };
            }
            catch (Exception ex)
            {
                return new PetListResultDTO
                {
                    Success = false,
                    Message = $"获取宠物列表失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取宠物详细信息
        /// </summary>
        /// <param name="request">宠物详情查询请求</param>
        /// <returns>宠物详情结果</returns>
        public async Task<PetDetailResultDTO> GetPetDetail(PetDetailRequestDTO request)
        {
            try
            {
                // 查询宠物信息
                var pet = _db.Queryable<user_pet>()
                    .Where(x => x.user_id == request.UserId && x.id == request.PetId)
                    .First();

                if (pet == null)
                {
                    return new PetDetailResultDTO
                    {
                        Success = false,
                        Message = "宠物不存在或不属于该用户"
                    };
                }

                // 查询宠物配置
                var petConfig = _db.Queryable<pet_config>()
                    .Where(x => x.pet_no == pet.pet_no)
                    .First();

                // 查询用户信息获取主宠物ID
                var user = _db.Queryable<user>().Where(x => x.id == request.UserId).First();

                // 构建宠物基础信息
                var petInfo = new PetInfoDTO
                {
                    Id = pet.id,
                    PetNo = pet.pet_no,
                    Name = petConfig?.name ?? "未知宠物",
                    Element =petConfig?.attribute ?? "无",
                    Level = CalculatePetLevel(pet.exp ?? 0),
                    Exp = pet.exp ?? 0,
                    Hp = pet.hp ?? 0,
                    Mp = pet.mp ?? 0,
                    Growth = pet.growth ?? 0,
                    Realm = pet.realm ?? "无境界",
                    IsMainPet = user?.main_pet_id == pet.id
                };

                // 计算宠物属性
                var attributes = await CalculateFinalAttributesAsync(new AttributeRequestDTO
                {
                    UserId = request.UserId,
                    PetId = pet.id
                });

                // 查询宠物技能
                var petSkills = _db.Queryable<user_pet_skill>()
                    .Where(x => x.user_pet_id == pet.id)
                    .ToList();

                var skills = petSkills.Select(skill => new PetSkillDTO
                {
                    SkillId = skill.skill_id,
                    SkillName = $"技能{skill.skill_id}", // 这里需要关联技能配置表获取名称
                    SkillLevel = skill.skill_level ?? 0,
                    Description = "技能描述" // 从技能配置表获取
                }).ToList();

                return new PetDetailResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    PetInfo = petInfo,
                    Attributes = attributes,
                    Skills = skills
                };
            }
            catch (Exception ex)
            {
                return new PetDetailResultDTO
                {
                    Success = false,
                    Message = $"获取宠物详情失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 设置主战宠物
        /// </summary>
        /// <param name="request">设置主战宠物请求</param>
        /// <returns>操作结果</returns>
        public LoginResultDTO SetMainPet(WebApplication_HM.DTOs.RequestDTO.SetMainPetRequestDTO request)
        {
            try
            {
                // 验证宠物是否属于该用户
                var pet = _db.Queryable<user_pet>()
                    .Where(x => x.user_id == request.UserId && x.id == request.PetId)
                    .First();

                if (pet == null)
                {
                    return new LoginResultDTO
                    {
                        Success = false,
                        Message = "宠物不存在或不属于该用户"
                    };
                }

                // 使用事务确保数据一致性
                var result = _db.Ado.UseTran(() =>
                {
                    // 1. 将该用户所有宠物的 is_main 设为 false
                    var resetResult = _db.Updateable<user_pet>()
                        .SetColumns(x => x.is_main == false)
                        .Where(x => x.user_id == request.UserId)
                        .ExecuteCommand();

                    // 2. 将指定宠物的 is_main 设为 true
                    var setMainResult = _db.Updateable<user_pet>()
                        .SetColumns(x => x.is_main == true)
                        .Where(x => x.user_id == request.UserId && x.id == request.PetId)
                        .ExecuteCommand();

                    // 3. 更新用户表中的主宠物ID
                    var updateUserResult = _db.Updateable<user>()
                        .SetColumns(x => x.main_pet_id == request.PetId)
                        .Where(x => x.id == request.UserId)
                        .ExecuteCommand();

                    // 检查所有操作是否成功
                    if (setMainResult <= 0)
                    {
                        throw new Exception("设置宠物主战状态失败");
                    }

                    if (updateUserResult <= 0)
                    {
                        throw new Exception("更新用户主宠物ID失败");
                    }

                    return new { Success = true, ResetCount = resetResult, SetMainCount = setMainResult, UpdateUserCount = updateUserResult };
                });

                if (result.IsSuccess)
                {
                    return new LoginResultDTO
                    {
                        Success = true,
                        Message = $"主战宠物设置成功，影响 {result.Data.SetMainCount} 条宠物记录"
                    };
                }
                else
                {
                    return new LoginResultDTO
                    {
                        Success = false,
                        Message = $"设置失败：{result.ErrorMessage}"
                    };
                }
            }
            catch (Exception ex)
            {
                return new LoginResultDTO
                {
                    Success = false,
                    Message = $"设置主战宠物失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 计算宠物最终属性（用于战斗和显示）
        /// </summary>
        /// <param name="request">属性计算请求</param>
        /// <returns>属性计算结果</returns>
        public AttributeResultDTO GetPetAttributes(AttributeRequestDTO request)
        {
            return CalculateFinalAttributes(request);
        }

        #endregion

        #region 地图战斗系统实现

        /// <summary>
        /// 获取地图列表
        /// </summary>
        /// <param name="request">地图列表查询请求</param>
        /// <returns>地图列表结果</returns>
        public MapListResultDTO GetMapList(MapListRequestDTO request)
        {
            try
            {
                // 查询所有地图配置
                var mapConfigs = _db.Queryable<map_config>()
                    .OrderBy(x => x.map_id)
                    .ToList();

                // 查询地图详细配置（用于判断解锁条件）
                var mapDetails = _db.Queryable<map_detail>().ToList();

                // 查询用户信息（用于判断解锁条件）
                var user = _db.Queryable<user>().Where(x => x.id == request.UserId).First();

                var mapList = mapConfigs.Select(map =>
                {
                    var detail = mapDetails.FirstOrDefault(d => d.map_id == map.map_id);
                    
                    // 简单的解锁逻辑：根据地图ID顺序解锁
                    bool isUnlocked = map.map_id <= 3; // 假设前3个地图默认解锁

                    return new MapInfoDTO
                    {
                        MapId = map.map_id,
                        MapName = map.map_name,
                        MapDesc = map.map_desc ?? "",
                        MapType = map.map_type ?? 0,
                        AtlastName = map.atlast_name ?? "",
                        Background = map.background ?? "",
                        Icon = map.ico ?? "",
                        IsUnlocked = isUnlocked,
                        RecommendLevel = detail?.limit_level ?? 1
                    };
                }).ToList();

                return new MapListResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    Maps = mapList
                };
            }
            catch (Exception ex)
            {
                return new MapListResultDTO
                {
                    Success = false,
                    Message = $"获取地图列表失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取地图详细信息
        /// </summary>
        /// <param name="request">地图详情查询请求</param>
        /// <returns>地图详情结果</returns>
        public MapDetailResultDTO GetMapDetail(MapDetailRequestDTO request)
        {
            try
            {
                // 查询地图基础配置
                var mapConfig = _db.Queryable<map_config>()
                    .Where(x => x.map_id == request.MapId)
                    .First();

                if (mapConfig == null)
                {
                    return new MapDetailResultDTO
                    {
                        Success = false,
                        Message = "地图不存在"
                    };
                }

                // 查询地图详细配置
                var mapDetail = _db.Queryable<map_detail>()
                    .Where(x => x.map_id == request.MapId)
                    .First();

                // 查询地图怪物
                var mapMonsters = _db.Queryable<map_monster>()
                    .Where(x => x.map_id == request.MapId)
                    .ToList();

                // 查询地图掉落配置
                var mapDrops = _db.Queryable<drop_config>()
                    .Where(x => x.map_id == request.MapId)
                    .ToList();

                // 查询道具配置（用于显示掉落物品名称）
                var itemConfigs = _db.Queryable<item_config>().ToList();

                // 构建地图基础信息
                var mapInfo = new MapInfoDTO
                {
                    MapId = mapConfig.map_id,
                    MapName = mapConfig.map_name,
                    MapDesc = mapConfig.map_desc ?? "",
                    MapType = mapConfig.map_type ?? 0,
                    AtlastName = mapConfig.atlast_name ?? "",
                    Background = mapConfig.background ?? "",
                    Icon = mapConfig.ico ?? "",
                    IsUnlocked = true, // 既然能查询详情，说明已解锁
                    RecommendLevel = mapDetail?.limit_level ?? 1
                };

                // 构建地图详细配置
                var detailConfig = mapDetail != null ? new MapDetailConfigDTO
                {
                    LimitLevel = mapDetail.limit_level ?? 0,
                    LimitGrowth = mapDetail.limit_growth ?? 0,
                    RequireKey = mapDetail.limit_key ?? false,
                    MinGold = mapDetail.min_gold ?? 0,
                    MaxGold = mapDetail.max_gold ?? 0,
                    MinYuanbao = mapDetail.min_yuanbao ?? 0,
                    MaxYuanbao = mapDetail.max_yuanbao ?? 0
                } : null;

                // 构建怪物列表
                var monsters = mapMonsters.Select(monster => new MapMonsterDTO
                {
                    MonsterId = monster.monster_id ?? 0,
                    MonsterName = monster.monster_name ?? "未知怪物",
                    MinLevel = monster.min_level ?? 0,
                    MaxLevel = monster.max_level ?? 0,
                    Element = monster.element ?? "无",
                    ExpReward = monster.exp ?? 0,
                    Growth = monster.growth ?? 0
                }).ToList();

                // 构建掉落列表
                var drops = mapDrops.Select(drop =>
                {
                    var itemConfig = itemConfigs.FirstOrDefault(i => i.item_no.ToString() == drop.item_id);
                    return new MapDropDTO
                    {
                        ItemId = int.TryParse(drop.item_id, out int itemId) ? itemId : 0,
                        ItemName = itemConfig?.name ?? $"道具{drop.item_id}",
                        DropType = drop.drop_type,
                        DropRate = drop.drop_rate ?? 0,
                        MinQuantity = drop.min_count ?? 0,
                        MaxQuantity = drop.max_count ?? 0
                    };
                }).ToList();

                return new MapDetailResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    MapInfo = mapInfo,
                    DetailConfig = detailConfig,
                    Monsters = monsters,
                    Drops = drops
                };
            }
            catch (Exception ex)
            {
                return new MapDetailResultDTO
                {
                    Success = false,
                    Message = $"获取地图详情失败：{ex.Message}"
                };
            }
        }

        #endregion

        #region 配置数据系统实现

        /// <summary>
        /// 获取游戏配置数据
        /// </summary>
        /// <param name="request">配置查询请求</param>
        /// <returns>游戏配置结果</returns>
        public GameConfigResultDTO GetGameConfig(ConfigRequestDTO request)
        {
            try
            {
                var result = new GameConfigResultDTO { Success = true, Message = "获取成功" };

                // 根据配置类型返回对应数据
                switch (request.ConfigType.ToLower())
                {
                    case "pet":
                        result = GetPetConfigs();
                        break;
                    case "skill":
                        result = GetSkillConfigs();
                        break;
                    case "item":
                        result = GetItemConfigs();
                        break;
                    case "monster":
                        result = GetMonsterConfigs();
                        break;
                    case "realm":
                        result = GetRealmConfigs();
                        break;
                    case "equipment":
                        result = GetEquipmentConfigs();
                        break;
                    case "all":
                    default:
                        // 获取所有配置
                        var petResult = GetPetConfigs();
                        var skillResult = GetSkillConfigs();
                        var itemResult = GetItemConfigs();
                        var monsterResult = GetMonsterConfigs();
                        var realmResult = GetRealmConfigs();
                        var equipmentResult = GetEquipmentConfigs();

                        result.PetConfigs = petResult.PetConfigs;
                        result.SkillConfigs = skillResult.SkillConfigs;
                        result.ItemConfigs = itemResult.ItemConfigs;
                        result.MonsterConfigs = monsterResult.MonsterConfigs;
                        result.RealmConfigs = realmResult.RealmConfigs;
                        result.EquipmentConfigs = equipmentResult.EquipmentConfigs;
                        break;
                }

                return result;
            }
            catch (Exception ex)
            {
                return new GameConfigResultDTO
                {
                    Success = false,
                    Message = $"获取配置失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取宠物配置列表
        /// </summary>
        /// <returns>宠物配置结果</returns>
        public GameConfigResultDTO GetPetConfigs()
        {
            try
            {
                var petConfigs = _db.Queryable<pet_config>().ToList();

                var configs = petConfigs.Select(pet => new PetConfigDTO
                {
                    PetNo = pet.pet_no,
                    Name = pet.name,
                    Attribute = pet.attribute,
                    Skills = pet.skill ?? ""
                }).ToList();

                return new GameConfigResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    PetConfigs = configs
                };
            }
            catch (Exception ex)
            {
                return new GameConfigResultDTO
                {
                    Success = false,
                    Message = $"获取宠物配置失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取技能配置列表
        /// </summary>
        /// <returns>技能配置结果</returns>
        public GameConfigResultDTO GetSkillConfigs()
        {
            try
            {
                var skillConfigs = _db.Queryable<skill>().ToList();

                var configs = skillConfigs.Select(skill => new DTOs.ResultDTO.SkillConfigDTO
                {
                    SkillId = skill.skill_id,
                    SkillName = skill.skill_name,
                    SkillPercent = skill.skill_percent,
                    EffectType = skill.effect_type?.ToString() ?? "",
                    ManaCost = skill.mana_cost,
                    ElementLimit = skill.element_limit?.ToString() ?? ""
                }).ToList();

                return new GameConfigResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    SkillConfigs = configs
                };
            }
            catch (Exception ex)
            {
                return new GameConfigResultDTO
                {
                    Success = false,
                    Message = $"获取技能配置失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取道具配置列表
        /// </summary>
        /// <returns>道具配置结果</returns>
        public GameConfigResultDTO GetItemConfigs()
        {
            try
            {
                var itemConfigs = _db.Queryable<item_config>().ToList();

                var configs = itemConfigs.Select(item => new ItemConfigDTO
                {
                    ItemNo = item.item_no,
                    Name = item.name,
                    Type = item.type ?? "",
                    Description = item.description ?? "",
                    Quality = item.quality ?? "",
                    Icon = item.icon ?? "",
                    Price = item.price ?? 0
                }).ToList();

                return new GameConfigResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    ItemConfigs = configs
                };
            }
            catch (Exception ex)
            {
                return new GameConfigResultDTO
                {
                    Success = false,
                    Message = $"获取道具配置失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取怪物配置列表
        /// </summary>
        /// <returns>怪物配置结果</returns>
        public GameConfigResultDTO GetMonsterConfigs()
        {
            try
            {
                var monsterConfigs = _db.Queryable<monster_config>().ToList();

                var configs = monsterConfigs.Select(monster => new MonsterConfigDTO
                {
                    MonsterNo = monster.monster_no,
                    Name = monster.name,
                    Attribute = monster.attribute,
                    Skill = monster.skill ?? ""
                }).ToList();

                return new GameConfigResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    MonsterConfigs = configs
                };
            }
            catch (Exception ex)
            {
                return new GameConfigResultDTO
                {
                    Success = false,
                    Message = $"获取怪物配置失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取境界配置列表
        /// </summary>
        /// <returns>境界配置结果</returns>
        public GameConfigResultDTO GetRealmConfigs()
        {
            try
            {
                var realmConfigs = _db.Queryable<realm_config>().ToList();

                var configs = realmConfigs.Select(realm => new RealmConfigDTO
                {
                    RealmId = realm.realm_id,
                    RealmName = realm.realm_name
                }).ToList();

                return new GameConfigResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    RealmConfigs = configs
                };
            }
            catch (Exception ex)
            {
                return new GameConfigResultDTO
                {
                    Success = false,
                    Message = $"获取境界配置失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取装备配置列表
        /// </summary>
        /// <returns>装备配置结果</returns>
        public GameConfigResultDTO GetEquipmentConfigs()
        {
            try
            {
                var equipmentConfigs = _db.Queryable<equipment>()
                    .LeftJoin<equipment_type>((e, et) => e.equip_type_id == et.equip_type_id)
                    .Select((e, et) => new EquipmentConfigDTO
                    {
                        EquipId = e.equip_id,
                        Name = e.name,
                        EquipTypeId = e.equip_type_id,
                        TypeName = et.type_name ?? "",
                        Element = e.element ?? "",
                        Icon = e.icon ?? ""
                    })
                    .ToList();

                return new GameConfigResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    EquipmentConfigs = equipmentConfigs
                };
            }
            catch (Exception ex)
            {
                return new GameConfigResultDTO
                {
                    Success = false,
                    Message = $"获取装备配置失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取用户装备背包
        /// </summary>
        /// <param name="request">装备列表查询请求</param>
        /// <returns>装备列表结果</returns>
        public EquipmentListResultDTO GetUserEquipments(EquipmentListRequestDTO request)
        {
            try
            {
                var userEquipments = _db.Queryable<user_equipment>()
                    .Where(ue => ue.user_id == request.UserId)
                    .OrderBy(ue => ue.is_equipped, OrderByType.Desc) // 已装备的排在前面
                    .OrderBy(ue => ue.equip_type_id)
                    .OrderBy(ue => ue.create_time)
                    .ToList();

                var equipments = userEquipments.Select(ue => new UserEquipmentDTO
                {
                    Id = ue.id,
                    EquipId = ue.equip_id,
                    Name = ue.name,
                    Icon = ue.icon ?? "",
                    EquipTypeId = ue.equip_type_id,
                    StrengthenLevel = ue.strengthen_level ?? 0,
                    Slot = ue.slot ?? 0,
                    Position = ue.position,
                    IsEquipped = ue.is_equipped ?? false,
                    CreateTime = ue.create_time ?? DateTime.Now
                }).ToList();

                return new EquipmentListResultDTO
                {
                    Success = true,
                    Message = "获取成功",
                    Equipments = equipments
                };
            }
            catch (Exception ex)
            {
                return new EquipmentListResultDTO
                {
                    Success = false,
                    Message = $"获取用户装备失败：{ex.Message}"
                };
            }
        }

        #endregion

        #region 装备操作实现

        /// <summary>
        /// 装备道具
        /// </summary>
        /// <param name="request">装备请求</param>
        /// <returns>操作结果</returns>
        public LoginResultDTO EquipItem(EquipRequestDTO request)
        {
            try
            {
                // 查询装备是否存在且属于该用户
                var equipment = _db.Queryable<user_equipment>()
                    .Where(x => x.id == request.EquipmentId && x.user_id == request.UserId)
                    .First();

                if (equipment == null)
                {
                    return new LoginResultDTO
                    {
                        Success = false,
                        Message = "装备不存在或不属于该用户"
                    };
                }

                // 检查装备是否已装备
                if (equipment.is_equipped == true)
                {
                    return new LoginResultDTO
                    {
                        Success = false,
                        Message = "装备已经装备中"
                    };
                }

                // 查询装备配置信息
                var equipmentConfig = _db.Queryable<equipment>()
                    .Where(x => x.equip_id == equipment.equip_id)
                    .First();

                if (equipmentConfig == null)
                {
                    return new LoginResultDTO
                    {
                        Success = false,
                        Message = "装备配置不存在"
                    };
                }

                // 检查该位置是否已有装备，如果有则先卸下
                var existingEquipment = _db.Queryable<user_equipment>()
                    .Where(x => x.user_id == request.UserId && 
                               x.position == request.Position && 
                               (x.is_equipped == true || x.is_equipped == null))
                    .First();

                if (existingEquipment != null)
                {
                    // 卸下原有装备
                    _db.Updateable<user_equipment>()
                        .SetColumns(x => new user_equipment 
                        { 
                            is_equipped = false, 
                            position = 0 
                        })
                        .Where(x => x.id == existingEquipment.id)
                        .ExecuteCommand();
                }

                // 装备新装备
                var updateResult = _db.Updateable<user_equipment>()
                    .SetColumns(x => new user_equipment 
                    { 
                        is_equipped = true, 
                        position = request.Position 
                    })
                    .Where(x => x.id == request.EquipmentId)
                    .ExecuteCommand();

                if (updateResult > 0)
                {
                    return new LoginResultDTO
                    {
                        Success = true,
                        Message = "装备成功"
                    };
                }
                else
                {
                    return new LoginResultDTO
                    {
                        Success = false,
                        Message = "装备失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return new LoginResultDTO
                {
                    Success = false,
                    Message = $"装备失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 卸下装备
        /// </summary>
        /// <param name="request">卸装请求</param>
        /// <returns>操作结果</returns>
        public LoginResultDTO UnequipItem(UnequipRequestDTO request)
        {
            try
            {
                // 查询装备是否存在且属于该用户
                var equipment = _db.Queryable<user_equipment>()
                    .Where(x => x.id == request.EquipmentId && x.user_id == request.UserId)
                    .First();

                if (equipment == null)
                {
                    return new LoginResultDTO
                    {
                        Success = false,
                        Message = "装备不存在或不属于该用户"
                    };
                }

                // 检查装备是否已装备
                if (equipment.is_equipped != true)
                {
                    return new LoginResultDTO
                    {
                        Success = false,
                        Message = "装备未装备"
                    };
                }

                // 卸下装备
                var updateResult = _db.Updateable<user_equipment>()
                    .SetColumns(x => new user_equipment 
                    { 
                        is_equipped = false, 
                        position = 0 
                    })
                    .Where(x => x.id == request.EquipmentId)
                    .ExecuteCommand();

                if (updateResult > 0)
                {
                    return new LoginResultDTO
                    {
                        Success = true,
                        Message = "卸下装备成功"
                    };
                }
                else
                {
                    return new LoginResultDTO
                    {
                        Success = false,
                        Message = "卸下装备失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return new LoginResultDTO
                {
                    Success = false,
                    Message = $"卸下装备失败：{ex.Message}"
                };
            }
        }

        #endregion

        #region 背包道具系统实现

        /// <summary>
        /// 获取背包物品列表
        /// </summary>
        /// <param name="request">背包查询请求</param>
        /// <returns>背包结果</returns>
        public BagResultDTO GetBag(BagRequestDTO request)
        {
            try
            {
                // 查询用户的所有道具
                var userItems = _db.Queryable<user_item>()
                    .Where(x => x.user_id == request.UserId && x.item_count > 0)
                    .OrderBy(x => x.item_seq)
                    .ToList();

                var bagItems = new List<BagItemDTO>();

                foreach (var userItem in userItems)
                {
                    // 转换item_id为item_no进行配置查询
                    if (int.TryParse(userItem.item_id, out int itemNo))
                    {
                        var itemConfig = _db.Queryable<item_config>()
                            .Where(x => x.item_no == itemNo)
                            .First();

                        if (itemConfig != null)
                        {
                            bagItems.Add(new BagItemDTO
                            {
                                ItemId = userItem.item_id,
                                ItemNo = itemConfig.item_no,
                                Name = itemConfig.name ?? "未知道具",
                                Type = itemConfig.type ?? "其他",
                                Description = itemConfig.description ?? "",
                                Quality = itemConfig.quality ?? "普通",
                                Icon = itemConfig.icon ?? "",
                                Price = itemConfig.price ?? 0,
                                ItemCount = userItem.item_count,
                                ItemPos = userItem.item_pos,
                                ItemSeq = userItem.item_seq,
                                CanUse = !string.IsNullOrEmpty(itemConfig.type) && 
                                        (itemConfig.type.Contains("药水") || itemConfig.type.Contains("卷轴")),
                                CanSell = itemConfig.price > 0
                            });
                        }
                    }
                }

                return new BagResultDTO
                {
                    Success = true,
                    Message = "获取背包成功",
                    Items = bagItems,
                    MaxCapacity = 100, // 默认背包容量
                    UsedCapacity = bagItems.Count
                };
            }
            catch (Exception ex)
            {
                return new BagResultDTO
                {
                    Success = false,
                    Message = $"获取背包失败：{ex.Message}",
                    Items = new List<BagItemDTO>(),
                    MaxCapacity = 100,
                    UsedCapacity = 0
                };
            }
        }

        /// <summary>
        /// 使用道具
        /// </summary>
        /// <param name="request">使用道具请求</param>
        /// <returns>使用结果</returns>
        public UseItemResultDTO UseItem(UseItemRequestDTO request)
        {
            try
            {
                // 开始事务
                _db.Ado.BeginTran();

                // 查询用户道具（通过ItemId查找）
                var userItem = _db.Queryable<user_item>()
                    .Where(x => x.user_id == request.UserId && x.item_id == request.ItemId)
                    .First();

                if (userItem == null)
                {
                    _db.Ado.RollbackTran();
                    return new UseItemResultDTO
                    {
                        Success = false,
                        Message = "道具不存在"
                    };
                }

                if (userItem.item_count < request.UseCount)
                {
                    _db.Ado.RollbackTran();
                    return new UseItemResultDTO
                    {
                        Success = false,
                        Message = "道具数量不足"
                    };
                }

                // 查询道具配置
                if (!int.TryParse(request.ItemId, out int itemNo))
                {
                    _db.Ado.RollbackTran();
                    return new UseItemResultDTO
                    {
                        Success = false,
                        Message = "道具ID格式错误"
                    };
                }

                var itemConfig = _db.Queryable<item_config>()
                    .Where(x => x.item_no == itemNo)
                    .First();

                if (itemConfig == null)
                {
                    _db.Ado.RollbackTran();
                    return new UseItemResultDTO
                    {
                        Success = false,
                        Message = "道具配置不存在"
                    };
                }

                // 检查道具是否可使用
                if (string.IsNullOrEmpty(itemConfig.type) || 
                    (!itemConfig.type.Contains("药水") && !itemConfig.type.Contains("卷轴")))
                {
                    _db.Ado.RollbackTran();
                    return new UseItemResultDTO
                    {
                        Success = false,
                        Message = "该道具无法使用"
                    };
                }

                // 减少道具数量
                var newCount = userItem.item_count - request.UseCount;
                if (newCount <= 0)
                {
                    // 删除道具记录
                    _db.Deleteable<user_item>()
                        .Where(x => x.id == userItem.id)
                        .ExecuteCommand();
                    newCount = 0;
                }
                else
                {
                    // 更新道具数量
                    _db.Updateable<user_item>()
                        .SetColumns(x => x.item_count == newCount)
                        .Where(x => x.id == userItem.id)
                        .ExecuteCommand();
                }

                // 应用道具效果（简化实现）
                var result = new UseItemResultDTO
                {
                    Success = true,
                    Message = "使用道具成功",
                    ItemId = request.ItemId,
                    UsedCount = request.UseCount,
                    RemainingCount = newCount,
                    EffectDescription = GetItemEffect(itemConfig),
                    GainedExp = 0,
                    GainedGold = 0,
                    GainedYuanbao = 0,
                    RestoredHp = 0,
                    RestoredMp = 0
                };

                // 根据道具类型应用效果
                ApplyItemEffect(itemConfig, request.UseCount, request.UserId, result);

                _db.Ado.CommitTran();
                return result;
            }
            catch (Exception ex)
            {
                _db.Ado.RollbackTran();
                return new UseItemResultDTO
                {
                    Success = false,
                    Message = $"使用道具失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 出售道具
        /// </summary>
        /// <param name="request">出售道具请求</param>
        /// <returns>出售结果</returns>
        public SellItemResultDTO SellItem(SellItemRequestDTO request)
        {
            try
            {
                // 开始事务
                _db.Ado.BeginTran();

                // 查询用户道具（通过ItemId查找）
                var userItem = _db.Queryable<user_item>()
                    .Where(x => x.user_id == request.UserId && x.item_id == request.ItemId)
                    .First();

                if (userItem == null)
                {
                    _db.Ado.RollbackTran();
                    return new SellItemResultDTO
                    {
                        Success = false,
                        Message = "道具不存在"
                    };
                }

                if (userItem.item_count < request.SellCount)
                {
                    _db.Ado.RollbackTran();
                    return new SellItemResultDTO
                    {
                        Success = false,
                        Message = "道具数量不足"
                    };
                }

                // 查询道具配置
                if (!int.TryParse(request.ItemId, out int itemNo))
                {
                    _db.Ado.RollbackTran();
                    return new SellItemResultDTO
                    {
                        Success = false,
                        Message = "道具ID格式错误"
                    };
                }

                var itemConfig = _db.Queryable<item_config>()
                    .Where(x => x.item_no == itemNo)
                    .First();

                if (itemConfig == null)
                {
                    _db.Ado.RollbackTran();
                    return new SellItemResultDTO
                    {
                        Success = false,
                        Message = "道具配置不存在"
                    };
                }

                // 检查道具是否可出售
                var sellPrice = itemConfig.price ?? 0;
                if (sellPrice <= 0)
                {
                    _db.Ado.RollbackTran();
                    return new SellItemResultDTO
                    {
                        Success = false,
                        Message = "该道具无法出售"
                    };
                }

                // 计算售价（通常为原价的一半）
                var unitPrice = sellPrice / 2;
                var totalPrice = unitPrice * request.SellCount;

                // 减少道具数量
                var newCount = userItem.item_count - request.SellCount;
                if (newCount <= 0)
                {
                    // 删除道具记录
                    _db.Deleteable<user_item>()
                        .Where(x => x.id == userItem.id)
                        .ExecuteCommand();
                    newCount = 0;
                }
                else
                {
                    // 更新道具数量
                    _db.Updateable<user_item>()
                        .SetColumns(x => x.item_count == newCount)
                        .Where(x => x.id == userItem.id)
                        .ExecuteCommand();
                }

                // 增加玩家金币
                _db.Updateable<user>()
                    .SetColumnsIF(true, x => x.gold == x.gold + totalPrice)
                    .Where(x => x.id == request.UserId)
                    .ExecuteCommand();

                _db.Ado.CommitTran();

                return new SellItemResultDTO
                {
                    Success = true,
                    Message = "出售道具成功",
                    ItemId = request.ItemId,
                    SoldCount = request.SellCount,
                    RemainingCount = newCount,
                    GainedGold = totalPrice,
                    UnitPrice = unitPrice,
                    TotalPrice = totalPrice
                };
            }
            catch (Exception ex)
            {
                _db.Ado.RollbackTran();
                return new SellItemResultDTO
                {
                    Success = false,
                    Message = $"出售道具失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 整理背包
        /// </summary>
        /// <param name="request">整理背包请求</param>
        /// <returns>整理结果</returns>
        public SortBagResultDTO SortBag(SortBagRequestDTO request)
        {
            try
            {
                // 开始事务
                _db.Ado.BeginTran();

                // 查询用户的所有道具
                var userItems = _db.Queryable<user_item>()
                    .Where(x => x.user_id == request.UserId && x.item_count > 0)
                    .ToList();

                var beforeCount = userItems.Count;

                // 按道具ID分组，合并相同道具
                var itemGroups = userItems.GroupBy(x => x.item_id).ToList();
                var mergedCount = 0;

                foreach (var group in itemGroups)
                {
                    var items = group.ToList();
                    if (items.Count > 1)
                    {
                        // 需要合并
                        var totalCount = items.Sum(x => x.item_count);
                        var firstItem = items.First();

                        // 更新第一个道具的数量
                        _db.Updateable<user_item>()
                            .SetColumns(x => x.item_count == totalCount)
                            .Where(x => x.id == firstItem.id)
                            .ExecuteCommand();

                        // 删除其他重复道具
                        var otherItems = items.Skip(1).ToList();
                        foreach (var item in otherItems)
                        {
                            _db.Deleteable<user_item>()
                                .Where(x => x.id == item.id)
                                .ExecuteCommand();
                            mergedCount++;
                        }
                    }
                }

                // 重新排序item_seq
                var remainingItems = _db.Queryable<user_item>()
                    .Where(x => x.user_id == request.UserId && x.item_count > 0)
                    .OrderBy(x => x.item_id)
                    .ToList();

                for (int i = 0; i < remainingItems.Count; i++)
                {
                    _db.Updateable<user_item>()
                        .SetColumns(x => x.item_seq == i + 1)
                        .Where(x => x.id == remainingItems[i].id)
                        .ExecuteCommand();
                }

                var afterCount = remainingItems.Count;

                _db.Ado.CommitTran();

                return new SortBagResultDTO
                {
                    Success = true,
                    Message = "背包整理成功",
                    BeforeCount = beforeCount,
                    AfterCount = afterCount,
                    MergedCount = mergedCount,
                    Details = $"合并了{mergedCount}个重复道具，整理后共{afterCount}种道具"
                };
            }
            catch (Exception ex)
            {
                _db.Ado.RollbackTran();
                return new SortBagResultDTO
                {
                    Success = false,
                    Message = $"背包整理失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取道具效果描述
        /// </summary>
        /// <param name="itemConfig">道具配置</param>
        /// <returns>效果描述</returns>
        private string GetItemEffect(item_config itemConfig)
        {
            if (itemConfig.type?.Contains("药水") == true)
            {
                if (itemConfig.name?.Contains("生命") == true)
                    return "恢复生命值";
                if (itemConfig.name?.Contains("法力") == true)
                    return "恢复法力值";
                if (itemConfig.name?.Contains("经验") == true)
                    return "增加经验值";
            }
            else if (itemConfig.type?.Contains("卷轴") == true)
            {
                return "使用卷轴效果";
            }

            return itemConfig.description ?? "使用道具";
        }

        /// <summary>
        /// 应用道具效果
        /// </summary>
        /// <param name="itemConfig">道具配置</param>
        /// <param name="useCount">使用数量</param>
        /// <param name="userId">用户ID</param>
        /// <param name="result">结果对象</param>
        private void ApplyItemEffect(item_config itemConfig, int useCount, int userId, UseItemResultDTO result)
        {
            // 简化的效果应用逻辑
            if (itemConfig.type?.Contains("药水") == true)
            {
                if (itemConfig.name?.Contains("生命") == true)
                {
                    result.RestoredHp = 100 * useCount; // 每个生命药水恢复100生命值
                    result.EffectDescription = $"恢复{result.RestoredHp}点生命值";
                }
                else if (itemConfig.name?.Contains("法力") == true)
                {
                    result.RestoredMp = 50 * useCount; // 每个法力药水恢复50法力值
                    result.EffectDescription = $"恢复{result.RestoredMp}点法力值";
                }
                else if (itemConfig.name?.Contains("经验") == true)
                {
                    result.GainedExp = 200 * useCount; // 每个经验药水增加200经验
                    result.EffectDescription = $"获得{result.GainedExp}点经验";
                }
            }
            else if (itemConfig.type?.Contains("卷轴") == true)
            {
                // 金币卷轴
                if (itemConfig.name?.Contains("金币") == true)
                {
                    result.GainedGold = 500 * useCount; // 每个金币卷轴获得500金币
                    result.EffectDescription = $"获得{result.GainedGold}金币";
                    
                    // 更新玩家金币
                    _db.Updateable<user>()
                        .SetColumnsIF(true, x => x.gold == x.gold + result.GainedGold)
                        .Where(x => x.id == userId)
                        .ExecuteCommand();
                }
                // 元宝卷轴
                else if (itemConfig.name?.Contains("元宝") == true)
                {
                    result.GainedYuanbao = 10 * useCount; // 每个元宝卷轴获得10元宝
                    result.EffectDescription = $"获得{result.GainedYuanbao}元宝";
                    
                    // 更新玩家元宝
                    _db.Updateable<user>()
                        .SetColumnsIF(true, x => x.yuanbao == x.yuanbao + result.GainedYuanbao)
                        .Where(x => x.id == userId)
                        .ExecuteCommand();
                }
            }
        }

        /// <summary>
        /// 添加或更新用户物品
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="itemId">物品ID</param>
        /// <param name="count">数量</param>
        private void AddOrUpdateUserItem(int userId, string itemId, long count)
        {
            try
            {
                var existingItem = _db.Queryable<user_item>()
                    .Where(x => x.user_id == userId && x.item_id == itemId)
                    .First();

                if (existingItem != null)
                {
                    // 更新现有物品数量
                    var currentCount = existingItem.item_count;
                    var newCount = currentCount + count;
                    _db.Updateable<user_item>()
                        .SetColumns(x => new user_item
                        {
                            item_count = newCount
                        })
                        .Where(x => x.id == existingItem.id)
                        .ExecuteCommand();
                }
                else
                {
                    // 获取下一个序号
                    var maxSeq = _db.Queryable<user_item>()
                        .Where(x => x.user_id == userId)
                        .Max(x => x.item_seq);

                    // 添加新物品
                    var newItem = new user_item
                    {
                        user_id = userId,
                        item_id = itemId,
                        item_count = count,
                        item_pos = 1, // 默认放入背包
                        item_seq = maxSeq + 1, // 序号递增
                        create_time = DateTime.Now
                    };
                    _db.Insertable(newItem).ExecuteCommand();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加用户物品失败: UserId={UserId}, ItemId={ItemId}, Count={Count}", userId, itemId, count);
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 格式化技能显示字符串 - 专为Battle.html设计
        /// 返回格式：技能名|类型|技能ID|消耗|是否可用,技能名2|类型2|技能ID2|消耗2|是否可用2
        /// </summary>
        /// <param name="petSkills">宠物技能列表</param>
        /// <returns>格式化的技能字符串</returns>
        private string FormatSkillDisplayForBattle(List<user_pet_skill> petSkills)
        {
            if (petSkills == null || petSkills.Count == 0)
            {
                return "普通攻击|攻击|null|0|false";
            }

            var skillStrings = new List<string>();

            // 添加普通攻击
            skillStrings.Add("普通攻击|攻击|null|0|false");

            // 添加宠物技能
            foreach (var skill in petSkills.Take(4)) // 最多显示4个技能
            {
                try
                {
                    // 获取技能配置信息
                    var skillConfig = _db.Queryable<skill>()
                        .Where(x => x.skill_id == skill.skill_id.ToString())
                        .First();

                    if (skillConfig != null)
                    {
                        string skillName = skillConfig.skill_name ?? $"技能{skill.skill_id}";
                        string skillType = skillConfig.skill_type ?? "攻击";
                        string skillId = skill.skill_id.ToString();
                        string mpCost = skillConfig.mana_cost.ToString();
                        string isAvailable = "false"; // 简化处理，实际应该检查MP是否足够

                        skillStrings.Add($"{skillName}|{skillType}|{skillId}|{mpCost}|{isAvailable}");
                    }
                }
                catch (Exception ex)
                {
                    // 如果获取技能配置失败，添加默认技能
                    skillStrings.Add($"未知技能|攻击|{skill.skill_id}|10|false");
                }
            }

            return string.Join(",", skillStrings);
        }

        /// <summary>
        /// 计算宠物最大生命值
        /// </summary>
        /// <param name="pet">宠物信息</param>
        /// <returns>最大生命值</returns>
        private long CalculateMaxHp(user_pet pet)
        {
            if (pet.max_hp.HasValue && pet.max_hp.Value > 0)
                return pet.max_hp.Value;

            // 基于等级和成长计算最大生命值
            int level = CalculatePetLevel(pet.exp ?? 0);
            decimal growth = pet.growth ?? 1.0m;

            // 基础生命值 + 等级加成 + 成长加成
            long baseHp = 100;
            long levelBonus = level * 50;
            long growthBonus = (long)(baseHp * (growth - 1.0m));

            return Math.Max(100, baseHp + levelBonus + growthBonus);
        }

        /// <summary>
        /// 计算宠物最大魔法值
        /// </summary>
        /// <param name="pet">宠物信息</param>
        /// <returns>最大魔法值</returns>
        private long CalculateMaxMp(user_pet pet)
        {
            if (pet.max_mp.HasValue && pet.max_mp.Value > 0)
                return pet.max_mp.Value;

            // 基于等级和成长计算最大魔法值
            int level = CalculatePetLevel(pet.exp ?? 0);
            decimal growth = pet.growth ?? 1.0m;

            // 基础魔法值 + 等级加成 + 成长加成
            long baseMp = 50;
            long levelBonus = level * 25;
            long growthBonus = (long)(baseMp * (growth - 1.0m));

            return Math.Max(50, baseMp + levelBonus + growthBonus);
        }

        /// <summary>
        /// 根据经验计算宠物等级（使用新的等级服务）
        /// </summary>
        /// <param name="exp">当前经验</param>
        /// <returns>等级</returns>
        private async Task<int> CalculatePetLevelAsync(long exp)
        {
            try
            {
                return await _levelService.CalculateLevelAsync(exp, "pet");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算宠物等级失败，经验值: {Exp}", exp);
                // 降级到简化计算
                int level = (int)(exp / 1000) + 1;
                return Math.Max(1, Math.Min(level, 100));
            }
        }

        /// <summary>
        /// 根据经验计算宠物等级（同步版本，用于兼容现有代码）
        /// </summary>
        /// <param name="exp">当前经验</param>
        /// <returns>等级</returns>
        private int CalculatePetLevel(long exp)
        {
            try
            {
                // 使用异步方法的同步版本
                return CalculatePetLevelAsync(exp).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算宠物等级失败，经验值: {Exp}", exp);
                // 降级到简化计算
                int level = (int)(exp / 1000) + 1;
                return Math.Max(1, Math.Min(level, 100));
            }
        }

        /// <summary>
        /// 获取宠物的五行属性（通过pet_no关联pet_config表）
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <returns>五行属性</returns>
        private string GetPetAttribute(int petNo)
        {
            try
            {
                var petConfig = _db.Queryable<pet_config>().Where(x => x.pet_no == petNo).First();
                return petConfig?.attribute ?? "无";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物属性失败，宠物编号: {PetNo}", petNo);
                return "无";
            }
        }

        /// <summary>
        /// 获取宠物的五行属性（异步版本）
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <returns>五行属性</returns>
        private async Task<string> GetPetAttributeAsync(int petNo)
        {
            try
            {
                var petConfig = await _db.Queryable<pet_config>().Where(x => x.pet_no == petNo).FirstAsync();
                return petConfig?.attribute ?? "无";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物属性失败，宠物编号: {PetNo}", petNo);
                return "无";
            }
        }

        /// <summary>
        /// 获取怪物等级 (基于怪物配置或随机生成)
        /// </summary>
        /// <param name="monster">怪物信息</param>
        /// <returns>怪物等级</returns>
        private int GetMonsterLevel(map_monster monster)
        {
            try
            {
                // 如果怪物有等级范围配置，在范围内随机生成
                if (monster.min_level.HasValue && monster.max_level.HasValue)
                {
                    int minLevel = monster.min_level.Value;
                    int maxLevel = monster.max_level.Value;

                    // 确保最小值不大于最大值
                    if (minLevel > maxLevel)
                    {
                        (minLevel, maxLevel) = (maxLevel, minLevel);
                    }

                    var random = new Random();
                    return random.Next(minLevel, maxLevel + 1);
                }

                // 如果没有等级配置，基于怪物经验值估算等级
                if (monster.exp.HasValue && monster.exp.Value > 0)
                {
                    // 简化计算：经验值 / 1000 + 1
                    int estimatedLevel = (int)(monster.exp.Value / 1000) + 1;
                    return Math.Max(1, Math.Min(estimatedLevel, 200)); // 限制在1-200级
                }

                // 默认等级
                return 50;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取怪物等级失败，怪物ID: {MonsterId}", monster.id);
                return 50; // 默认等级
            }
        }

        #endregion

        #region 技能系统统一集成

        /// <summary>
        /// 计算被动技能属性加成（技能系统统一）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="petId">宠物数据库ID</param>
        /// <returns>被动技能属性加成字典</returns>
        private async Task<Dictionary<string, double>> CalculatePassiveSkillAttributes(int userId, int petId)
        {
            var result = new Dictionary<string, double>
            {
                {"攻击", 0}, {"防御", 0}, {"命中", 0}, {"闪避", 0}, {"速度", 0},
                {"生命", 0}, {"魔法", 0}, {"加深", 0}, {"抵消", 0}, {"吸血", 0}, {"吸魔", 0}
            };

            try
            {
                // 获取宠物的被动技能列表
                var passiveSkills = await _skillService.GetPetPassiveSkillsAsync(petId);

                foreach (var skill in passiveSkills)
                {
                    if (skill.SkillConfig == null || !skill.SkillConfig.IsBuff) continue;

                    var effectType = skill.SkillConfig.EffectType;
                    if (string.IsNullOrEmpty(effectType) || effectType == "null") continue;

                    // 被动效果 = 基础效果 + 技能等级 * 0.005
                    var effectValue = skill.CalculateEffectValue();

                    // 处理多效果技能（用逗号分隔）
                    var effectTypes = effectType.Split(',');
                    foreach (var singleEffect in effectTypes)
                    {
                        var trimmedEffect = singleEffect.Trim();
                        if (result.ContainsKey(trimmedEffect))
                        {
                            result[trimmedEffect] += effectValue;
                        }
                    }
                }

                _logger.LogDebug("计算被动技能属性加成完成，PetId: {PetId}, 效果数量: {EffectCount}",
                    petId, result.Count(kv => kv.Value > 0));

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算被动技能属性加成失败，UserId: {UserId}, PetId: {PetId}", userId, petId);
                return result; // 返回空的加成，避免影响属性计算
            }
        }

        /// <summary>
        /// 计算主动技能伤害倍数（用于战斗计算）
        /// </summary>
        /// <param name="petId">宠物数据库ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>技能伤害倍数</returns>
        private async Task<double> CalculateActiveSkillMultiplier(int petId, string? skillId)
        {
            try
            {
                if (string.IsNullOrEmpty(skillId))
                {
                    return 1.0; // 普通攻击
                }

                return await _skillService.CalculateSkillMultiplierAsync(petId, skillId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算主动技能倍数失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return 1.0; // 降级为普通攻击
            }
        }

        /// <summary>
        /// 计算主动技能魔法消耗（用于战斗计算）
        /// </summary>
        /// <param name="petId">宠物数据库ID</param>
        /// <param name="skillId">技能ID</param>
        /// <returns>魔法消耗</returns>
        private async Task<int> CalculateActiveSkillManaCost(int petId, string? skillId)
        {
            try
            {
                if (string.IsNullOrEmpty(skillId))
                {
                    return 0; // 普通攻击无消耗
                }

                return await _skillService.CalculateManaCostAsync(petId, skillId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算主动技能魔法消耗失败，PetId: {PetId}, SkillId: {SkillId}", petId, skillId);
                return 0; // 降级为无消耗
            }
        }

        /// <summary>
        /// 获取物品名称
        /// </summary>
        /// <param name="itemId">物品ID</param>
        /// <returns>物品名称</returns>
        private string GetItemName(string itemId)
        {
            try
            {
                int item_no = Convert.ToInt32(itemId);

                // 查询物品配置表获取物品名称
                var itemConfig = _db.Queryable<item_config>()
                    .Where(x => x.item_no == item_no)
                    .First();

                if (itemConfig == null|| itemConfig.item_no<=0) {
                    return "未知物品";
                }

                return itemConfig.name.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取物品名称失败，ItemId: {ItemId}", itemId);
                return $"未知物品({itemId})";
            }
        }

        #endregion

        #region 宠物筛选方法

        /// <summary>
        /// 获取可合成宠物列表（五系宠物）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>可合成宠物列表</returns>
        public PetListResultDTO GetSynthesisAvailablePets(int userId)
        {
            try
            {
                // 五系宠物类型
                var fiveElements = new[] { "金", "木", "水", "火", "土" };

                // 关联查询 user_pet 和 pet_config 表，直接在数据库层筛选五系宠物
                var query = _db.Queryable<user_pet, pet_config>((up, pc) => new JoinQueryInfos(
                    JoinType.Inner, up.pet_no == pc.pet_no))
                    .Where((up, pc) => up.user_id == userId)
                    .Where((up, pc) => up.status != "丢弃") // 排除已丢弃的宠物
                    .Where((up, pc) => fiveElements.Contains(pc.attribute)) // 从pet_config表筛选五系宠物
                    .Where((up, pc) => up.hp > 0) // 生命值>0（状态正常）
                    .Where((up, pc) => !string.IsNullOrEmpty(pc.name)) // 宠物配置名字不为空
                    .Where((up, pc) => pc.name != "涅槃兽") // 排除涅槃兽
                    .Where((up, pc) => !pc.name.Contains("涅槃重生")) // 排除涅槃重生宠物
                    .Select((up, pc) => new
                    {
                        // user_pet 表字段
                        PetId = up.id,
                        UserId = up.user_id,
                        PetNo = up.pet_no,
                        CustomName = up.custom_name,
                        Level = up.level,
                        Exp = up.exp,
                        Hp = up.hp,
                        Mp = up.mp,
                        Growth = up.growth,
                        Realm = up.realm,
                        Status = up.status,
                        IsMain = up.is_main,
                        // pet_config 表字段
                        ConfigName = pc.name,
                        Attribute = pc.attribute
                    })
                    .ToList();

                // 获取用户信息
                var user = _db.Queryable<user>().Where(x => x.id == userId).First();

                // 转换为DTO
                var petList = query.Select(pet => new PetInfoDTO
                {
                    Id = pet.PetId,
                    PetNo = pet.PetNo,
                    Name = pet.CustomName ?? pet.ConfigName ?? "未知宠物", // 优先使用自定义名称，其次使用配置名称
                    Element = pet.Attribute ?? "无", // 从pet_config表获取属性
                    Level = CalculatePetLevel(pet.Exp ?? 0),
                    Exp = pet.Exp ?? 0,
                    Hp = pet.Hp ?? 0,
                    Mp = pet.Mp ?? 0,
                    Growth = pet.Growth ?? 0,
                    Realm = pet.Realm ?? "无境界",
                    IsMainPet = user?.main_pet_id == pet.PetId,
                    Status = pet.Status,
                    IsMain = pet.IsMain
                }).ToList();

                return new PetListResultDTO
                {
                    Success = true,
                    Message = $"获取可合成宠物成功，共{petList.Count}只五系宠物",
                    Pets = petList,
                    MainPetId = user?.main_pet_id,
                    TotalCount = petList.Count,
                    RanchCount = petList.Count(p => p.Status == "牧场"),
                    CarryCount = petList.Count(p => p.Status == "携带")
                };
            }
            catch (Exception ex)
            {
                return new PetListResultDTO
                {
                    Success = false,
                    Message = $"获取可合成宠物列表失败：{ex.Message}",
                    Pets = new List<PetInfoDTO>()
                };
            }
        }

        /// <summary>
        /// 获取可涅槃宠物列表（神系宠物）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>可涅槃宠物列表</returns>
        public PetListResultDTO GetNirvanaAvailablePets(int userId)
        {
            try
            {
                // 神系宠物类型
                var godElements = new[] { "神", "神圣", "聖", "佛", "魔", "人", "鬼", "巫", "萌", "仙", "灵", "次元" };

                // 60级所需经验值
                var level60Exp = GetExpForLevel(60);

                // 关联查询 user_pet 和 pet_config 表，直接在数据库层筛选神系宠物
                var query = _db.Queryable<user_pet, pet_config>((up, pc) => new JoinQueryInfos(
                    JoinType.Inner, up.pet_no == pc.pet_no))
                    .Where((up, pc) => up.user_id == userId)
                    .Where((up, pc) => up.status != "丢弃") // 排除已丢弃的宠物
                    .Where((up, pc) => godElements.Contains(pc.attribute)) // 从pet_config表筛选神系宠物
                    .Where((up, pc) => (up.exp ?? 0) >= level60Exp) // 涅槃等级要求：60级以上
                    .Where((up, pc) => up.hp > 0) // 生命值>0（状态正常）
                    .Where((up, pc) => !string.IsNullOrEmpty(pc.name)) // 宠物配置名字不为空
                    .Where((up, pc) => pc.name != "涅槃兽") // 排除涅槃兽
                    .Where((up, pc) => !pc.name.Contains("涅槃重生")) // 排除涅槃重生宠物
                    .Select((up, pc) => new
                    {
                        // user_pet 表字段
                        PetId = up.id,
                        UserId = up.user_id,
                        PetNo = up.pet_no,
                        CustomName = up.custom_name,
                        Level = up.level,
                        Exp = up.exp,
                        Hp = up.hp,
                        Mp = up.mp,
                        Growth = up.growth,
                        Realm = up.realm,
                        Status = up.status,
                        IsMain = up.is_main,
                        // pet_config 表字段
                        ConfigName = pc.name,
                        Attribute = pc.attribute
                    })
                    .ToList();

                // 获取用户信息
                var user = _db.Queryable<user>().Where(x => x.id == userId).First();

                // 转换为DTO
                var petList = query.Select(pet => new PetInfoDTO
                {
                    Id = pet.PetId,
                    PetNo = pet.PetNo,
                    Name = pet.CustomName ?? pet.ConfigName ?? "未知宠物", // 优先使用自定义名称，其次使用配置名称
                    Element = pet.Attribute ?? "无", // 从pet_config表获取属性
                    Level = CalculatePetLevel(pet.Exp ?? 0),
                    Exp = pet.Exp ?? 0,
                    Hp = pet.Hp ?? 0,
                    Mp = pet.Mp ?? 0,
                    Growth = pet.Growth ?? 0,
                    Realm = pet.Realm ?? "无境界",
                    IsMainPet = user?.main_pet_id == pet.PetId,
                    Status = pet.Status,
                    IsMain = pet.IsMain
                }).ToList();

                return new PetListResultDTO
                {
                    Success = true,
                    Message = $"获取可涅槃宠物成功，共{petList.Count}只神系宠物",
                    Pets = petList,
                    MainPetId = user?.main_pet_id,
                    TotalCount = petList.Count,
                    RanchCount = petList.Count(p => p.Status == "牧场"),
                    CarryCount = petList.Count(p => p.Status == "携带")
                };
            }
            catch (Exception ex)
            {
                return new PetListResultDTO
                {
                    Success = false,
                    Message = $"获取可涅槃宠物列表失败：{ex.Message}",
                    Pets = new List<PetInfoDTO>()
                };
            }
        }

        /// <summary>
        /// 获取可进化宠物列表（只包含携带的宠物）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>可进化宠物列表</returns>
        public PetListResultDTO GetEvolutionAvailablePets(int userId)
        {
            try
            {
                // 关联查询 user_pet 和 pet_config 表，只获取携带的宠物
                var query = _db.Queryable<user_pet, pet_config>((up, pc) => new JoinQueryInfos(
                    JoinType.Inner, up.pet_no == pc.pet_no))
                    .Where((up, pc) => up.user_id == userId)
                    .Where((up, pc) => up.status == "携带") // 只获取携带的宠物
                    .Where((up, pc) => up.hp > 0) // 生命值>0（状态正常）
                    .Where((up, pc) => !string.IsNullOrEmpty(pc.name)) // 宠物配置名字不为空
                    .Where((up, pc) => pc.name != "涅槃兽") // 排除涅槃兽
                    .Where((up, pc) => !pc.name.Contains("涅槃重生")) // 排除涅槃重生宠物
                    .Select((up, pc) => new
                    {
                        // user_pet 表字段
                        PetId = up.id,
                        UserId = up.user_id,
                        PetNo = up.pet_no,
                        CustomName = up.custom_name,
                        Level = up.level,
                        Exp = up.exp,
                        Hp = up.hp,
                        Mp = up.mp,
                        Growth = up.growth,
                        Realm = up.realm,
                        Status = up.status,
                        IsMain = up.is_main,
                        // pet_config 表字段
                        ConfigName = pc.name,
                        Attribute = pc.attribute
                    })
                    .ToList();

                // 获取用户信息
                var user = _db.Queryable<user>().Where(x => x.id == userId).First();

                // 转换为DTO
                var petList = query.Select(pet => new PetInfoDTO
                {
                    Id = pet.PetId,
                    PetNo = pet.PetNo,
                    Name = pet.CustomName ?? pet.ConfigName ?? "未知宠物", // 优先使用自定义名称，其次使用配置名称
                    Element = pet.Attribute ?? "无", // 从pet_config表获取属性
                    Level = CalculatePetLevel(pet.Exp ?? 0),
                    Exp = pet.Exp ?? 0,
                    Hp = pet.Hp ?? 0,
                    Mp = pet.Mp ?? 0,
                    Growth = pet.Growth ?? 0,
                    Realm = pet.Realm ?? "无境界",
                    IsMainPet = user?.main_pet_id == pet.PetId,
                    Status = pet.Status,
                    IsMain = pet.IsMain
                }).ToList();

                return new PetListResultDTO
                {
                    Success = true,
                    Message = $"获取可进化宠物成功，共{petList.Count}只携带宠物",
                    Pets = petList,
                    MainPetId = user?.main_pet_id,
                    TotalCount = petList.Count,
                    RanchCount = 0, // 进化只包含携带宠物，牧场数量为0
                    CarryCount = petList.Count
                };
            }
            catch (Exception ex)
            {
                return new PetListResultDTO
                {
                    Success = false,
                    Message = $"获取可进化宠物列表失败：{ex.Message}",
                    Pets = new List<PetInfoDTO>()
                };
            }
        }

        /// <summary>
        /// 根据等级计算所需经验值
        /// </summary>
        /// <param name="level">目标等级</param>
        /// <returns>所需经验值</returns>
        private int GetExpForLevel(int level)
        {
            if (level <= 1) return 0;

            // 简化的经验计算公式：level * level * 100
            // 实际项目中应该使用准确的经验表
            return level * level * 100;
        }

        #endregion
    }

    /// <summary>
    /// 掉落物品配置 DTO
    /// </summary>
    public class DropItemConfig
    {
        /// <summary>
        /// 物品ID
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 掉落概率（0~1之间）
        /// </summary>
        public double DropRate { get; set; }

        /// <summary>
        /// 最小掉落数量
        /// </summary>
        public int MinCount { get; set; } = 1;

        /// <summary>
        /// 最大掉落数量
        /// </summary>
        public int MaxCount { get; set; } = 1;
    }

    /// <summary>
    /// 战斗奖励临时存储类
    /// </summary>
    public class BattleReward
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        public int MapId { get; set; }

        /// <summary>
        /// 怪物ID
        /// </summary>
        public int MonsterId { get; set; }

        /// <summary>
        /// 是否胜利
        /// </summary>
        public bool IsWin { get; set; }

        /// <summary>
        /// 获得的经验值
        /// </summary>
        public int Experience { get; set; }

        /// <summary>
        /// 获得的金币
        /// </summary>
        public int Gold { get; set; }

        /// <summary>
        /// 获得的元宝
        /// </summary>
        public int Yuanbao { get; set; }

        /// <summary>
        /// 掉落物品列表
        /// </summary>
        public List<string> DropItems { get; set; } = new List<string>();

        /// <summary>
        /// 创建时间（用于过期清理）
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 战斗唯一标识
        /// </summary>
        public string BattleId { get; set; } = string.Empty;


    }
}