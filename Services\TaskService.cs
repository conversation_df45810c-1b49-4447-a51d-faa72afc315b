using SqlSugar;
using Newtonsoft.Json;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.DTOs;
using WebApplication_HM.Models.Define;
using TaskStatus = WebApplication_HM.Models.Define.TaskStatus;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 任务管理服务实现
    /// </summary>
    public class TaskService : ITaskService
    {
        private readonly ISqlSugarClient _db;
        private readonly ITaskConfigService _taskConfigService;
        private readonly ITaskRewardService _taskRewardService;
        private readonly ILevelService _levelService;
        private readonly ILogger<TaskService> _logger;

        public TaskService(
            ISqlSugarClient db,
            ITaskConfigService taskConfigService,
            ITaskRewardService taskRewardService,
            ILevelService levelService,
            ILogger<TaskService> logger)
        {
            _db = db;
            _taskConfigService = taskConfigService;
            _taskRewardService = taskRewardService;
            _levelService = levelService;
            _logger = logger;
        }

        #region 任务查询

        /// <summary>
        /// 获取用户任务列表
        /// </summary>
        public async Task<TaskListResponseDto> GetUserTaskListAsync(TaskListRequestDto request)
        {
            try
            {
                var result = new TaskListResponseDto
                {
                    PageIndex = request.PageIndex,
                    PageSize = request.PageSize
                };

                // 获取可接取任务
                result.AvailableTasks = await GetAvailableTasksAsync(request.UserId);

                // 获取进行中任务
                var inProgressQuery = _db.Queryable<user_task, task_config>((ut, tc) => new JoinQueryInfos(
                    JoinType.Inner, ut.task_id == tc.task_id))
                    .Where((ut, tc) => ut.user_id == request.UserId && ut.task_status == (byte)TaskStatus.InProgress);

                if (request.TaskType.HasValue)
                {
                    inProgressQuery = inProgressQuery.Where((ut, tc) => tc.task_type == request.TaskType.Value);
                }

                var inProgressTasks = await inProgressQuery
                    .Select((ut, tc) => new { UserTask = ut, TaskConfig = tc })
                    .ToPageListAsync(request.PageIndex, request.PageSize);

                result.InProgressTasks = await ConvertToUserTaskDtos(inProgressTasks.Select(x => x.UserTask).ToList());

                // 获取已完成任务
                var completedQuery = _db.Queryable<user_task, task_config>((ut, tc) => new JoinQueryInfos(
                    JoinType.Inner, ut.task_id == tc.task_id))
                    .Where((ut, tc) => ut.user_id == request.UserId && ut.task_status == (byte)TaskStatus.Completed);

                if (request.TaskType.HasValue)
                {
                    completedQuery = completedQuery.Where((ut, tc) => tc.task_type == request.TaskType.Value);
                }

                var completedTasks = await completedQuery
                    .OrderBy((ut, tc) => ut.completed_at, OrderByType.Desc)
                    .Select((ut, tc) => new { UserTask = ut, TaskConfig = tc })
                    .ToPageListAsync(request.PageIndex, request.PageSize);

                result.CompletedTasks = await ConvertToUserTaskDtos(completedTasks.Select(x => x.UserTask).ToList());

                result.TotalCount = result.AvailableTasks.Count + inProgressTasks.Count() + completedTasks.Count();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户任务列表失败，用户ID: {UserId}", request.UserId);
                throw;
            }
        }

        /// <summary>
        /// 获取可接取任务列表（优化版本：减少查询层级）
        /// </summary>
        public async Task<List<TaskInfoDto>> GetAvailableTasksAsync(int userId)
        {
            try
            {
                // 获取用户已接取的任务
                var userTasks = await _db.Queryable<user_task>()
                    .Where(ut => ut.user_id == userId && ut.task_status != (byte)TaskStatus.Abandoned)
                    .ToListAsync();

                // 一次性关联查询：task_config + task_objective + task_type_config
                var taskData = await _db.Queryable<task_config, task_objective, task_type_config>((tc, to, ttc) => new JoinQueryInfos(
                    JoinType.Inner, tc.task_id == to.task_id,
                    JoinType.Left, to.objective_type == ttc.type_id))
                    .Where((tc, to, ttc) => tc.is_active == 1)
                    .Select((tc, to, ttc) => new TaskObjectiveData
                    {
                        TaskConfig = tc,
                        Objective = to,
                        TypeConfig = ttc
                    })
                    .ToListAsync();

                // 按任务分组
                var taskGroups = taskData.GroupBy(x => x.TaskConfig.task_id).ToList();
                var availableTasks = new List<TaskInfoDto>();

                foreach (var taskGroup in taskGroups)
                {
                    var taskConfig = taskGroup.First().TaskConfig;

                    // 跳过已接取的任务(除非是可重复任务且已完成)
                    var userTask = userTasks.FirstOrDefault(ut => ut.task_id == taskConfig.task_id);
                    if (userTask != null)
                    {
                        if (taskConfig.is_repeatable == 0 || userTask.task_status == (byte)TaskStatus.InProgress)
                        {
                            continue;
                        }
                    }

                    // 检查前置任务
                    if (!string.IsNullOrEmpty(taskConfig.prerequisite_task))
                    {
                        var prerequisiteCompleted = userTasks.Any(ut =>
                            ut.task_id == taskConfig.prerequisite_task &&
                            ut.task_status == (byte)TaskStatus.Completed);

                        if (!prerequisiteCompleted)
                        {
                            continue;
                        }
                    }

                    // 直接构建TaskInfoDto，避免多层调用
                    var taskDto = await BuildTaskInfoDtoDirectly(taskConfig, taskGroup.ToList());
                    availableTasks.Add(taskDto);
                }

                return availableTasks.OrderBy(t => t.SortOrder).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可接取任务列表失败，用户ID: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// 获取已接受任务列表（优化版本：一次性关联查询所有相关表）
        /// </summary>
        public async Task<List<TaskInfoDto>> GetAcceptedTasksAsync(int userId)
        {
            try
            {
                // 简化查询：user_task + task_config + task_objective + task_type_config（不再查询进度表）
                var taskData = await _db.Queryable<user_task, task_config, task_objective, task_type_config>(
                    (ut, tc, to, ttc) => new JoinQueryInfos(
                        JoinType.Inner, ut.task_id == tc.task_id,
                        JoinType.Inner, tc.task_id == to.task_id,
                        JoinType.Left, to.objective_type == ttc.type_id))
                    .Where((ut, tc, to, ttc) => ut.user_id == userId && ut.task_status == (byte)TaskStatus.InProgress)
                    .Select((ut, tc, to, ttc) => new AcceptedTaskData
                    {
                        UserTask = ut,
                        TaskConfig = tc,
                        Objective = to,
                        TypeConfig = ttc
                    })
                    .ToListAsync();

                // 按任务分组并构建TaskInfoDto
                var taskGroups = taskData.GroupBy(x => x.TaskConfig.task_id).ToList();
                var result = new List<TaskInfoDto>();

                foreach (var taskGroup in taskGroups)
                {
                    var taskConfig = taskGroup.First().TaskConfig;
                    var userTask = taskGroup.First().UserTask;

                    // 构建任务目标列表
                    var objectiveDtos = new List<TaskObjectiveDto>();
                    foreach (var data in taskGroup)
                    {
                        var objective = data.Objective;

                        // 直接查询实际数据获取进度
                        int currentAmount = await GetObjectiveCurrentAmountAsync(userId, objective);
                        bool isCompleted = currentAmount >= objective.target_amount;
                        decimal completionPercentage = objective.target_amount > 0 ?
                            Math.Round((decimal)currentAmount / objective.target_amount * 100, 2) : 0;

                        // 处理进度模板显示
                        string progressDisplay = "";
                        if (!string.IsNullOrEmpty(objective.complete_template))
                        {
                            var completionStatus = isCompleted ? "已完成" : "未完成";
                            progressDisplay = string.Format(objective.complete_template, currentAmount, completionStatus);
                        }

                        objectiveDtos.Add(new TaskObjectiveDto
                        {
                            ObjectiveId = objective.objective_id,
                            ObjectiveType = objective.objective_type,
                            ObjectiveTypeName = data.TypeConfig?.type_name,
                            TargetId = objective.target_id,
                            TargetAmount = objective.target_amount,
                            ObjectiveDescription = objective.objective_description,
                            CurrentAmount = currentAmount,
                            IsCompleted = isCompleted,
                            CompletionPercentage = completionPercentage,
                            ProgressDisplay = progressDisplay
                        });
                    }

                    // 构建TaskInfoDto
                    var taskInfo = new TaskInfoDto
                    {
                        TaskId = taskConfig.task_id,
                        TaskName = taskConfig.task_name,
                        TaskDescription = taskConfig.task_description,
                        TaskType = taskConfig.task_type ?? 0,
                        TaskTypeName = GetTaskTypeName((byte)(taskConfig.task_type ?? 0)),
                        IsRepeatable = (taskConfig.is_repeatable ?? 0) == 1,
                        PrerequisiteTask = taskConfig.prerequisite_task,
                        RequiredPet = taskConfig.required_pet,
                        IsNetworkTask = (taskConfig.is_network_task ?? 0) == 1,
                        SortOrder = taskConfig.sort_order ?? 0,
                        Objectives = objectiveDtos,
                        Rewards = taskConfig.reward_description
                    };

                    result.Add(taskInfo);
                }

                return result.OrderBy(t => t.SortOrder).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取已接受任务列表失败，用户ID: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// 获取用户进行中的任务
        /// </summary>
        public async Task<List<UserTaskDto>> GetInProgressTasksAsync(int userId)
        {
            try
            {
                var userTasks = await _db.Queryable<user_task>()
                    .Where(ut => ut.user_id == userId && ut.task_status == (byte)TaskStatus.InProgress)
                    .OrderBy(ut => ut.accepted_at)
                    .ToListAsync();

                return await ConvertToUserTaskDtos(userTasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户进行中任务失败，用户ID: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// 获取任务详情
        /// </summary>
        public async Task<TaskDetailResponseDto> GetTaskDetailAsync(int userId, string taskId)
        {
            try
            {
                var result = new TaskDetailResponseDto();

                // 检查用户是否已接取任务
                var userTask = await _db.Queryable<user_task>()
                    .Where(ut => ut.user_id == userId && ut.task_id == taskId)
                    .FirstAsync();

                if (userTask != null)
                {
                    result.IsAccepted = true;
                    result.UserTask = (await ConvertToUserTaskDtos(new List<user_task> { userTask })).First();
                }
                else
                {
                    result.IsAccepted = false;
                    var (canAccept, message) = await CheckTaskAcceptabilityAsync(userId, taskId);
                    result.AcceptabilityMessage = message;

                    if (canAccept)
                    {
                        var taskConfig = await _taskConfigService.GetTaskConfigAsync(taskId);
                        if (taskConfig != null)
                        {
                            var taskInfo = await ConvertToTaskInfoDto(taskConfig);
                            result.UserTask = new UserTaskDto
                            {
                                TaskInfo = taskInfo,
                                TaskStatus = (byte)TaskStatus.InProgress,
                                TaskStatusName = "未接取"
                            };
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务详情失败，用户ID: {UserId}, 任务ID: {TaskId}", userId, taskId);
                throw;
            }
        }

        /// <summary>
        /// 检查任务是否可接取
        /// </summary>
        public async Task<(bool CanAccept, string Message)> CheckTaskAcceptabilityAsync(int userId, string taskId)
        {
            try
            {
                // 检查任务是否存在且激活
                var taskConfig = await _taskConfigService.GetTaskConfigAsync(taskId);
                if (taskConfig == null)
                {
                    return (false, "任务不存在");
                }

                if (taskConfig.is_active == 0)
                {
                    return (false, "任务未激活");
                }

                // 检查是否已接取
                var existingTask = await _db.Queryable<user_task>()
                    .Where(ut => ut.user_id == userId && ut.task_id == taskId && ut.task_status == (byte)TaskStatus.InProgress)
                    .FirstAsync();

                if (existingTask != null)
                {
                    return (false, "任务已接取");
                }

                // 检查前置任务
                if (!string.IsNullOrEmpty(taskConfig.prerequisite_task))
                {
                    var prerequisiteCompleted = await _db.Queryable<user_task>()
                        .Where(ut => ut.user_id == userId && 
                                   ut.task_id == taskConfig.prerequisite_task && 
                                   ut.task_status == (byte)TaskStatus.Completed)
                        .AnyAsync();

                    if (!prerequisiteCompleted)
                    {
                        return (false, "前置任务未完成");
                    }
                }

                // 检查指定宠物(如果有)
                if (!string.IsNullOrEmpty(taskConfig.required_pet))
                {
                    // 检查用户是否拥有指定的宠物
                    var hasPet = await _db.Queryable<user_pet>()
                        .Where(up => up.user_id == userId && up.pet_no.ToString() == taskConfig.required_pet)
                        .AnyAsync();

                    if (!hasPet)
                    {
                        return (false, "需要拥有指定的宠物");
                    }
                }

                return (true, "可以接取任务");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查任务可接取性失败，用户ID: {UserId}, 任务ID: {TaskId}", userId, taskId);
                return (false, "检查失败");
            }
        }

        #endregion

        #region 任务操作

        /// <summary>
        /// 接取任务
        /// </summary>
        public async Task<TaskOperationResultDto> AcceptTaskAsync(AcceptTaskRequestDto request)
        {
            try
            {
                var result = new TaskOperationResultDto();

                // 检查是否可以接取
                var (canAccept, message) = await CheckTaskAcceptabilityAsync(request.UserId, request.TaskId);
                if (!canAccept)
                {
                    result.Success = false;
                    result.Message = message;
                    return result;
                }

                // 开始事务
                _db.Ado.BeginTran();

                try
                {
                    // 创建用户任务记录
                    var userTask = new user_task
                    {
                        user_id = request.UserId,
                        task_id = request.TaskId,
                        task_status = (byte)TaskStatus.InProgress,
                        accepted_at = DateTime.Now
                    };

                    var userTaskId = await _db.Insertable(userTask).ExecuteReturnIdentityAsync();
                    userTask.user_task_id = userTaskId;

                    // 创建任务进度记录
                    var objectives = await _taskConfigService.GetTaskObjectivesAsync(request.TaskId);
                    var progressList = objectives.Select(obj => new user_task_progress
                    {
                        user_task_id = userTaskId,
                        objective_id = obj.objective_id,
                        current_amount = 0,
                        is_completed = 0
                    }).ToList();

                    if (progressList.Any())
                    {
                        await _db.Insertable(progressList).ExecuteCommandAsync();
                    }

                    _db.Ado.CommitTran();

                    result.Success = true;
                    result.Message = "任务接取成功";
                    result.UpdatedTask = (await ConvertToUserTaskDtos(new List<user_task> { userTask })).First();

                    _logger.LogInformation("用户接取任务成功，用户ID: {UserId}, 任务ID: {TaskId}", request.UserId, request.TaskId);
                }
                catch
                {
                    _db.Ado.RollbackTran();
                    throw;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "接取任务失败，用户ID: {UserId}, 任务ID: {TaskId}", request.UserId, request.TaskId);
                return new TaskOperationResultDto
                {
                    Success = false,
                    Message = "接取任务失败"
                };
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 转换为TaskInfoDto（主要用于未接取的任务，显示默认进度）
        /// </summary>
        private async Task<TaskInfoDto> ConvertToTaskInfoDto(task_config taskConfig)
        {
            try
            {
                var objectives = await _taskConfigService.GetTaskObjectivesAsync(taskConfig.task_id);
                var rewards = await _taskRewardService.ParseRewardConfigAsync(taskConfig.reward_config);

                // 直接在这里处理目标转换，从task_type_config获取type_name
                var objectiveDtos = new List<TaskObjectiveDto>();

                foreach (var objective in objectives)
                {
                    try
                    {
                        // 从task_type_config表获取type_name
                        var typeConfig = await _db.Queryable<task_type_config>()
                            .Where(ttc => ttc.type_id == objective.objective_type)
                            .FirstAsync();

                        // 使用默认进度（未接取任务）
                        int currentAmount = 0;
                        bool isCompleted = false;
                        decimal completionPercentage = 0;

                        // 处理进度模板显示
                        string progressDisplay = "";
                        if (!string.IsNullOrEmpty(objective.complete_template))
                        {
                            var completionStatus = isCompleted ? "已完成" : "未完成";
                            progressDisplay = string.Format(objective.complete_template, currentAmount, completionStatus);
                        }

                        objectiveDtos.Add(new TaskObjectiveDto
                        {
                            ObjectiveId = objective.objective_id,
                            ObjectiveType = objective.objective_type,
                            ObjectiveTypeName = typeConfig?.type_name,
                            TargetId = objective.target_id,
                            TargetAmount = objective.target_amount,
                            ObjectiveDescription = objective.objective_description,
                            CurrentAmount = currentAmount,
                            IsCompleted = isCompleted,
                            CompletionPercentage = completionPercentage,
                            ProgressDisplay = progressDisplay
                        });
                    }
                    catch (Exception ex)
                    {
                        throw;
                    }
                }

                return new TaskInfoDto
                {
                    TaskId = taskConfig.task_id,
                    TaskName = taskConfig.task_name,
                    TaskDescription = taskConfig.task_description,
                    TaskType = taskConfig.task_type ?? 0,
                    TaskTypeName = GetTaskTypeName((byte)(taskConfig.task_type ?? 0)),
                    IsRepeatable = (taskConfig.is_repeatable ?? 0) == 1,
                    PrerequisiteTask = taskConfig.prerequisite_task,
                    RequiredPet = taskConfig.required_pet,
                    IsNetworkTask = (taskConfig.is_network_task ?? 0) == 1,
                    SortOrder = taskConfig.sort_order ?? 0,
                    Objectives = objectiveDtos,
                    Rewards = taskConfig.reward_description
                };
            }
            catch (Exception ex)
            {

                throw;
            }

        
        }

        /// <summary>
        /// 任务目标数据传输类（用于关联查询结果）
        /// </summary>
        private class TaskObjectiveData
        {
            public task_config TaskConfig { get; set; }
            public task_objective Objective { get; set; }
            public task_type_config TypeConfig { get; set; }
        }

        /// <summary>
        /// 已接受任务数据传输类（用于关联查询结果，包含进度信息）
        /// </summary>
        private class AcceptedTaskData
        {
            public user_task UserTask { get; set; }
            public task_config TaskConfig { get; set; }
            public task_objective Objective { get; set; }
            public task_type_config TypeConfig { get; set; }
        }

        /// <summary>
        /// 用户任务详情数据传输类（用于ConvertToUserTaskDtos的关联查询结果）
        /// </summary>
        private class UserTaskDetailData
        {
            public task_config TaskConfig { get; set; }
            public task_objective Objective { get; set; }
            public task_type_config TypeConfig { get; set; }
        }

        /// <summary>
        /// 直接构建TaskInfoDto（优化版本：减少查询层级）
        /// </summary>
        private async Task<TaskInfoDto> BuildTaskInfoDtoDirectly(task_config taskConfig, List<TaskObjectiveData> taskObjectiveData)
        {
            var rewards = await _taskRewardService.ParseRewardConfigAsync(taskConfig.reward_config);

            // 直接从关联查询结果构建目标列表
            var objectiveDtos = taskObjectiveData.Select(data =>
            {
                // 处理进度模板显示
                string progressDisplay = "";
                if (!string.IsNullOrEmpty(data.Objective.complete_template))
                {
                    var currentAmount = 0;
                    var isCompleted = false;
                    var completionStatus = isCompleted ? "已完成" : "未完成";
                    progressDisplay = string.Format(data.Objective.complete_template, currentAmount, completionStatus);
                }

                return new TaskObjectiveDto
                {
                    ObjectiveId = data.Objective.objective_id,
                    ObjectiveType = data.Objective.objective_type,
                    ObjectiveTypeName = data.TypeConfig?.type_name,
                    TargetId = data.Objective.target_id,
                    TargetAmount = data.Objective.target_amount,
                    ObjectiveDescription = data.Objective.objective_description,
                    CurrentAmount = 0,
                    IsCompleted = false,
                    CompletionPercentage = 0,
                    ProgressDisplay = progressDisplay
                };
            }).ToList();

            return new TaskInfoDto
            {
                TaskId = taskConfig.task_id,
                TaskName = taskConfig.task_name,
                TaskDescription = taskConfig.task_description,
                TaskType = taskConfig.task_type ?? 0,
                TaskTypeName = GetTaskTypeName((byte)(taskConfig.task_type ?? 0)),
                IsRepeatable = (taskConfig.is_repeatable ?? 0) == 1,
                PrerequisiteTask = taskConfig.prerequisite_task,
                RequiredPet = taskConfig.required_pet,
                IsNetworkTask = (taskConfig.is_network_task ?? 0) == 1,
                SortOrder = taskConfig.sort_order ?? 0,
                Objectives = objectiveDtos,
                Rewards = taskConfig.reward_description
            };
        }

        /// <summary>
        /// 转换为UserTaskDto列表（简化版：直接查询实际数据获取进度）
        /// </summary>
        private async Task<List<UserTaskDto>> ConvertToUserTaskDtos(List<user_task> userTasks)
        {
            if (!userTasks.Any()) return new List<UserTaskDto>();

            var taskIds = userTasks.Select(ut => ut.task_id).ToList();

            // 查询任务配置和目标数据（不再查询进度表）
            var taskData = await _db.Queryable<task_config, task_objective, task_type_config>(
                (tc, to, ttc) => new JoinQueryInfos(
                    JoinType.Inner, tc.task_id == to.task_id,
                    JoinType.Left, to.objective_type == ttc.type_id))
                .Where((tc, to, ttc) => taskIds.Contains(tc.task_id))
                .Select((tc, to, ttc) => new UserTaskDetailData
                {
                    TaskConfig = tc,
                    Objective = to,
                    TypeConfig = ttc
                })
                .ToListAsync();

            var result = new List<UserTaskDto>();

            foreach (var userTask in userTasks)
            {
                // 获取该用户任务的所有目标数据
                var taskObjectives = taskData.Where(td => td.TaskConfig.task_id == userTask.task_id).ToList();
                if (!taskObjectives.Any()) continue;

                var taskConfig = taskObjectives.First().TaskConfig;

                // 构建任务目标列表
                var objectiveDtos = new List<TaskObjectiveDto>();
                foreach (var data in taskObjectives)
                {
                    var objective = data.Objective;

                    // 直接查询实际数据获取进度（简化版）
                    int currentAmount = await GetObjectiveCurrentAmountAsync(userTask.user_id, objective);
                    bool isCompleted = currentAmount >= objective.target_amount;
                    decimal completionPercentage = objective.target_amount > 0 ?
                        Math.Round((decimal)currentAmount / objective.target_amount * 100, 2) : 0;

                    // 处理进度模板显示
                    string progressDisplay = "";
                    if (!string.IsNullOrEmpty(objective.complete_template))
                    {
                        var completionStatus = isCompleted ? "已完成" : "未完成";
                        progressDisplay = string.Format(objective.complete_template, currentAmount, completionStatus);
                    }

                    objectiveDtos.Add(new TaskObjectiveDto
                    {
                        ObjectiveId = objective.objective_id,
                        ObjectiveType = objective.objective_type,
                        ObjectiveTypeName = data.TypeConfig?.type_name,
                        TargetId = objective.target_id,
                        TargetAmount = objective.target_amount,
                        ObjectiveDescription = objective.objective_description,
                        CurrentAmount = currentAmount,
                        IsCompleted = isCompleted,
                        CompletionPercentage = completionPercentage,
                        ProgressDisplay = progressDisplay
                    });
                }

                // 获取任务奖励
                var rewards = await _taskRewardService.ParseRewardConfigAsync(taskConfig.reward_config);

                // 构建TaskInfoDto
                var taskInfo = new TaskInfoDto
                {
                    TaskId = taskConfig.task_id,
                    TaskName = taskConfig.task_name,
                    TaskDescription = taskConfig.task_description,
                    TaskType = taskConfig.task_type ?? 0,
                    TaskTypeName = GetTaskTypeName((byte)(taskConfig.task_type ?? 0)),
                    IsRepeatable = (taskConfig.is_repeatable ?? 0) == 1,
                    PrerequisiteTask = taskConfig.prerequisite_task,
                    RequiredPet = taskConfig.required_pet,
                    IsNetworkTask = (taskConfig.is_network_task ?? 0) == 1,
                    SortOrder = taskConfig.sort_order ?? 0,
                    Objectives = objectiveDtos,
                    Rewards = taskConfig.reward_description
                };

                // 计算总体完成度
                var overallCompletion = objectiveDtos.Count > 0 ?
                    Math.Round(objectiveDtos.Average(p => (double)p.CompletionPercentage), 2) : 0;

                var canComplete = objectiveDtos.All(p => p.IsCompleted) && (userTask.task_status ?? 0) == (byte)TaskStatus.InProgress;

                result.Add(new UserTaskDto
                {
                    UserTaskId = userTask.user_task_id,
                    TaskInfo = taskInfo,
                    TaskStatus = userTask.task_status ?? 0,
                    TaskStatusName = GetTaskStatusName((byte)(userTask.task_status ?? 0)),
                    AcceptedAt = userTask.accepted_at ?? DateTime.Now,
                    CompletedAt = userTask.completed_at,
                    CompletionCount = userTask.completion_count ?? 0,
                    Progress = objectiveDtos,
                    OverallCompletionPercentage = (decimal)overallCompletion,
                    CanComplete = canComplete
                });
            }

            return result;
        }



        /// <summary>
        /// 转换为TaskObjectiveDto（同步版本，保持向后兼容）
        /// </summary>
        private TaskObjectiveDto ConvertToTaskObjectiveDto(task_objective objective)
        {
            // 处理进度模板显示
            string progressDisplay = "";
            if (!string.IsNullOrEmpty(objective.complete_template))
            {
                var currentAmount = 0;
                var isCompleted = false;
                var completionStatus = isCompleted ? "已完成" : "未完成";
                progressDisplay = string.Format(objective.complete_template, currentAmount, completionStatus);
            }

            return new TaskObjectiveDto
            {
                ObjectiveId = objective.objective_id,
                ObjectiveType = objective.objective_type,
                ObjectiveTypeName = GetObjectiveTypeName(objective.objective_type),
                TargetId = objective.target_id,
                TargetAmount = objective.target_amount,
                ObjectiveDescription = objective.objective_description,
                CurrentAmount = 0,
                IsCompleted = false,
                CompletionPercentage = 0,
                ProgressDisplay = progressDisplay
            };
        }

        /// <summary>
        /// 获取任务类型名称
        /// </summary>
        private string GetTaskTypeName(byte taskType)
        {
            return taskType switch
            {
                0 => "普通任务",
                1 => "循环任务",
                2 => "活动任务",
                _ => "未知类型"
            };
        }

        /// <summary>
        /// 获取任务状态名称
        /// </summary>
        private string GetTaskStatusName(byte taskStatus)
        {
            return taskStatus switch
            {
                0 => "已完成",
                1 => "进行中",
                2 => "已放弃",
                _ => "未知状态"
            };
        }

        /// <summary>
        /// 获取目标类型名称（将英文常量映射为中文显示名称）
        /// </summary>
        private string GetObjectiveTypeName(string objectiveType)
        {
            return objectiveType switch
            {
                TaskObjectiveTypes.KILL_MONSTER => "击杀怪物",
                TaskObjectiveTypes.COLLECT_ITEM => "收集道具",
                TaskObjectiveTypes.REACH_LEVEL => "达到等级",
                TaskObjectiveTypes.CURRENCY => "货币任务",
                TaskObjectiveTypes.PET_GROWTH => "宠物成长",
                TaskObjectiveTypes.COLLECT_EQUIPMENT => "收集装备",
                TaskObjectiveTypes.VIP_LEVEL => "VIP等级",
                TaskObjectiveTypes.TIME_LIMIT => "时间限制",
                TaskObjectiveTypes.DUNGEON => "副本任务",
                TaskObjectiveTypes.CARD => "卡牌任务",
                TaskObjectiveTypes.ONE_CLICK => "一键完成",
                TaskObjectiveTypes.SPECIAL => "特殊任务",
                // 兼容性处理：如果数据库中还有旧的中文值
                "击杀怪物" => "击杀怪物",
                "收集道具" => "收集道具",
                "达到等级" => "达到等级",
                "货币" => "货币任务",
                "宠物成长" => "宠物成长",
                "收集装备" => "收集装备",
                "VIP等级" => "VIP等级",
                "时间限制" => "时间限制",
                "副本" => "副本任务",
                "卡牌" => "卡牌任务",
                "一键完成" => "一键完成",
                "特殊" => "特殊任务",
                _ => $"未知类型({objectiveType})"
            };
        }

        #endregion

        /// <summary>
        /// 更新等级相关任务进度（简化版：可选方法，用于保持进度表同步）
        /// 注意：在简化版任务系统中，此方法是可选的，因为完成检查不再依赖进度表
        /// </summary>
        public async Task UpdateLevelTaskProgressAsync(int userId, int newLevel)
        {
            try
            {
                // 获取用户所有进行中的等级相关任务
                var levelTasks = await _db.Queryable<user_task, task_objective>((ut, to) => new JoinQueryInfos(
                    JoinType.Inner, ut.task_id == to.task_id))
                    .Where((ut, to) => ut.user_id == userId &&
                                     ut.task_status == (byte)TaskStatus.InProgress &&
                                     to.objective_type == TaskObjectiveTypes.REACH_LEVEL)
                    .Select((ut, to) => new { UserTask = ut, Objective = to })
                    .ToListAsync();

                foreach (var taskInfo in levelTasks)
                {
                    // 检查等级是否达到目标
                    if (newLevel >= taskInfo.Objective.target_amount)
                    {
                        // 可选：更新任务进度表（用于保持数据一致性）
                        var progress = await _db.Queryable<user_task_progress>()
                            .Where(utp => utp.user_task_id == taskInfo.UserTask.user_task_id &&
                                        utp.objective_id == taskInfo.Objective.objective_id)
                            .FirstAsync();

                        if (progress != null)
                        {
                            progress.current_amount = newLevel;
                            progress.is_completed = 1;
                            progress.updated_at = DateTime.Now;

                            await _db.Updateable(progress).ExecuteCommandAsync();

                            _logger.LogInformation("更新等级任务进度表，用户ID: {UserId}, 任务ID: {TaskId}, 当前等级: {Level}",
                                userId, taskInfo.UserTask.task_id, newLevel);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新等级任务进度失败，用户ID: {UserId}, 等级: {Level}", userId, newLevel);
            }
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        public async Task<TaskOperationResultDto> CompleteTaskAsync(CompleteTaskRequestDto request)
        {
            try
            {
                var result = new TaskOperationResultDto();

                // 获取用户任务
                var userTask = await _db.Queryable<user_task>()
                    .Where(ut => ut.user_id == request.UserId &&
                               ut.task_id == request.TaskId &&
                               ut.task_status == (byte)TaskStatus.InProgress)
                    .FirstAsync();

                if (userTask == null)
                {
                    result.Success = false;
                    result.Message = "任务不存在或已完成";
                    return result;
                }

                // 检查任务是否可以完成
                var canComplete = await CheckTaskCanCompleteAsync(userTask.user_task_id, request.IsOneClick);
                if (!canComplete.CanComplete)
                {
                    result.Success = false;
                    result.Message = canComplete.Message;
                    return result;
                }

                // 开始事务
                _db.Ado.BeginTran();

                try
                {
                    // 获取任务配置和奖励
                    var taskConfig = await _taskConfigService.GetTaskConfigAsync(request.TaskId);
                    var rewards = await _taskRewardService.ParseRewardConfigAsync(taskConfig.reward_config);

                    // 发放奖励
                    var rewardGranted = await _taskRewardService.GrantTaskRewardsAsync(request.UserId, request.TaskId, rewards);
                    if (!rewardGranted)
                    {
                        _db.Ado.RollbackTran();
                        result.Success = false;
                        result.Message = "奖励发放失败";
                        return result;
                    }

                    // 更新任务结算状态
                    await UpdateTaskSettlementStatusAsync(request.UserId, request.TaskId);

                    // 更新任务状态
                    userTask.task_status = (byte)TaskStatus.Completed;
                    userTask.completed_at = DateTime.Now;
                    userTask.completion_count++;

                    await _db.Updateable(userTask).ExecuteCommandAsync();

                    // 如果是可重复任务，重置进度
                    if (taskConfig.is_repeatable == 1)
                    {
                        await _db.Updateable<user_task_progress>()
                            .SetColumns(utp => new user_task_progress
                            {
                                current_amount = 0,
                                is_completed = 0,
                                updated_at = DateTime.Now
                            })
                            .Where(utp => utp.user_task_id == userTask.user_task_id)
                            .ExecuteCommandAsync();

                        // 重新设置为进行中状态
                        userTask.task_status = (byte)TaskStatus.InProgress;
                        userTask.completed_at = null;
                        await _db.Updateable(userTask).ExecuteCommandAsync();
                    }

                    _db.Ado.CommitTran();

                    result.Success = true;
                    result.Message = "任务完成成功";
                    result.Rewards = rewards;
                    result.UpdatedTask = (await ConvertToUserTaskDtos(new List<user_task> { userTask })).First();

                    _logger.LogInformation("用户完成任务成功，用户ID: {UserId}, 任务ID: {TaskId}", request.UserId, request.TaskId);
                }
                catch
                {
                    _db.Ado.RollbackTran();
                    throw;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成任务失败，用户ID: {UserId}, 任务ID: {TaskId}", request.UserId, request.TaskId);
                return new TaskOperationResultDto
                {
                    Success = false,
                    Message = "完成任务失败"
                };
            }
        }

        /// <summary>
        /// 放弃任务
        /// </summary>
        public async Task<TaskOperationResultDto> AbandonTaskAsync(AbandonTaskRequestDto request)
        {
            try
            {
                var result = new TaskOperationResultDto();

                // 获取用户任务
                var userTask = await _db.Queryable<user_task>()
                    .Where(ut => ut.user_id == request.UserId &&
                               ut.task_id == request.TaskId &&
                               ut.task_status == (byte)TaskStatus.InProgress)
                    .FirstAsync();

                if (userTask == null)
                {
                    result.Success = false;
                    result.Message = "任务不存在或已完成";
                    return result;
                }

                // 更新任务状态
                userTask.task_status = (byte)TaskStatus.Abandoned;
                userTask.abandoned_at = DateTime.Now;

                await _db.Updateable(userTask).ExecuteCommandAsync();

                result.Success = true;
                result.Message = "任务放弃成功";
                result.UpdatedTask = (await ConvertToUserTaskDtos(new List<user_task> { userTask })).First();

                _logger.LogInformation("用户放弃任务成功，用户ID: {UserId}, 任务ID: {TaskId}", request.UserId, request.TaskId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "放弃任务失败，用户ID: {UserId}, 任务ID: {TaskId}", request.UserId, request.TaskId);
                return new TaskOperationResultDto
                {
                    Success = false,
                    Message = "放弃任务失败"
                };
            }
        }

        /// <summary>
        /// 刷新任务状态
        /// </summary>
        public async Task<bool> RefreshTaskStatusAsync(int userId)
        {
            try
            {
                // 获取用户所有进行中的任务
                var inProgressTasks = await _db.Queryable<user_task>()
                    .Where(ut => ut.user_id == userId && ut.task_status == (byte)TaskStatus.InProgress)
                    .ToListAsync();

                foreach (var userTask in inProgressTasks)
                {
                    // 检查任务是否应该自动完成
                    var canComplete = await CheckTaskCanCompleteAsync(userTask.user_task_id, false);
                    if (canComplete.CanComplete)
                    {
                        // 自动完成任务
                        await CompleteTaskAsync(new CompleteTaskRequestDto
                        {
                            UserId = userId,
                            TaskId = userTask.task_id,
                            IsOneClick = false
                        });
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新任务状态失败，用户ID: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 更新任务进度（简化版：可选方法，用于保持进度表同步）
        /// 注意：在简化版任务系统中，此方法是可选的，因为完成检查不再依赖进度表
        /// </summary>
        public async Task<bool> UpdateTaskProgressAsync(UpdateTaskProgressRequestDto request)
        {
            try
            {
                // 获取用户相关的任务
                var userTasks = await _db.Queryable<user_task, task_config, task_objective>((ut, tc, to) => new JoinQueryInfos(
                    JoinType.Inner, ut.task_id == tc.task_id,
                    JoinType.Inner, tc.task_id == to.task_id))
                    .Where((ut, tc, to) => ut.user_id == request.UserId &&
                                         ut.task_status == (byte)TaskStatus.InProgress &&
                                         to.objective_type == request.ObjectiveType &&
                                         (string.IsNullOrEmpty(request.TargetId) || to.target_id == request.TargetId))
                    .Select((ut, tc, to) => new { UserTask = ut, TaskConfig = tc, Objective = to })
                    .ToListAsync();

                if (!userTasks.Any())
                {
                    return true; // 没有相关任务，不需要更新
                }

                foreach (var taskInfo in userTasks)
                {
                    // 可选：获取当前进度并更新进度表（用于保持数据一致性）
                    var progress = await _db.Queryable<user_task_progress>()
                        .Where(utp => utp.user_task_id == taskInfo.UserTask.user_task_id &&
                                    utp.objective_id == taskInfo.Objective.objective_id)
                        .FirstAsync();

                    if (progress != null && progress.is_completed == 0)
                    {
                        // 更新进度表
                        progress.current_amount = Math.Min((progress.current_amount ?? 0) + request.Amount, taskInfo.Objective.target_amount);
                        progress.is_completed = (byte?)((progress.current_amount ?? 0) >= taskInfo.Objective.target_amount ? 1 : 0);
                        progress.updated_at = DateTime.Now;

                        await _db.Updateable(progress).ExecuteCommandAsync();
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务进度失败，用户ID: {UserId}, 目标类型: {ObjectiveType}",
                    request.UserId, request.ObjectiveType);
                return false;
            }
        }

        /// <summary>
        /// 批量更新任务进度
        /// </summary>
        public async Task<bool> BatchUpdateTaskProgressAsync(List<TaskProgressUpdateEventDto> events)
        {
            try
            {
                if (!events.Any()) return true;

                // 按用户分组处理
                var userGroups = events.GroupBy(e => e.UserId);

                foreach (var userGroup in userGroups)
                {
                    var userId = userGroup.Key;
                    var userEvents = userGroup.ToList();

                    // 按目标类型分组
                    var typeGroups = userEvents.GroupBy(e => new { e.ObjectiveType, e.TargetId });

                    foreach (var typeGroup in typeGroups)
                    {
                        var totalAmount = typeGroup.Sum(e => e.Amount);

                        await UpdateTaskProgressAsync(new UpdateTaskProgressRequestDto
                        {
                            UserId = userId,
                            ObjectiveType = typeGroup.Key.ObjectiveType,
                            TargetId = typeGroup.Key.TargetId,
                            Amount = totalAmount
                        });
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新任务进度失败");
                return false;
            }
        }

        /// <summary>
        /// 检查并自动完成任务
        /// </summary>
        public async Task<List<UserTaskDto>> CheckAndAutoCompleteTasksAsync(int userId)
        {
            try
            {
                var completedTasks = new List<UserTaskDto>();

                // 获取用户所有进行中的任务
                var inProgressTasks = await _db.Queryable<user_task>()
                    .Where(ut => ut.user_id == userId && ut.task_status == (byte)TaskStatus.InProgress)
                    .ToListAsync();

                foreach (var userTask in inProgressTasks)
                {
                    var canComplete = await CheckTaskCanCompleteAsync(userTask.user_task_id, false);
                    if (canComplete.CanComplete)
                    {
                        var result = await CompleteTaskAsync(new CompleteTaskRequestDto
                        {
                            UserId = userId,
                            TaskId = userTask.task_id,
                            IsOneClick = false
                        });

                        if (result.Success && result.UpdatedTask != null)
                        {
                            completedTasks.Add(result.UpdatedTask);
                        }
                    }
                }

                return completedTasks;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查并自动完成任务失败，用户ID: {UserId}", userId);
                return new List<UserTaskDto>();
            }
        }

        // End of task progress methods

        // Private helper methods

        /// <summary>
        /// 检查任务是否可以完成（简化版：直接检查用户实际数据）
        /// </summary>
        private async Task<(bool CanComplete, string Message)> CheckTaskCanCompleteAsync(int userTaskId, bool isOneClick)
        {
            try
            {
                // 获取用户任务和目标信息
                var taskData = await _db.Queryable<user_task, task_objective>((ut, to) => new JoinQueryInfos(
                    JoinType.Inner, ut.task_id == to.task_id))
                    .Where((ut, to) => ut.user_task_id == userTaskId)
                    .Select((ut, to) => new { UserTask = ut, Objective = to })
                    .ToListAsync();

                if (!taskData.Any())
                {
                    return (false, "任务没有目标");
                }

                var userId = taskData.First().UserTask.user_id;

                // 检查一键完成
                if (isOneClick)
                {
                    var hasOneClickObjective = taskData.Any(t => t.Objective.objective_type == TaskObjectiveTypes.ONE_CLICK);
                    if (!hasOneClickObjective)
                    {
                        return (false, "此任务不支持一键完成");
                    }

                    // 检查是否有一键完成道具
                    var hasOneClickItem = await CheckUserHasOneClickItemAsync(userId);
                    if (!hasOneClickItem)
                    {
                        return (false, "没有一键完成道具");
                    }
                    return (true, "可以一键完成");
                }

                // 检查所有目标是否完成（直接检查用户实际数据）
                foreach (var data in taskData)
                {
                    var objective = data.Objective;
                    var isCompleted = await CheckObjectiveCompletedAsync(userId, objective);

                    if (!isCompleted)
                    {
                        return (false, $"任务目标未完成：{objective.objective_description}");
                    }
                }

                return (true, "任务可以完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查任务完成状态失败，用户任务ID: {UserTaskId}", userTaskId);
                return (false, "检查失败");
            }
        }

        /// <summary>
        /// 检查目标是否完成（直接检查用户实际数据）
        /// </summary>
        private async Task<bool> CheckObjectiveCompletedAsync(int userId, task_objective objective)
        {
            try
            {
                switch (objective.objective_type)
                {
                    case TaskObjectiveTypes.KILL_MONSTER:
                        return await CheckKillMonsterObjectiveAsync(userId, objective);

                    case TaskObjectiveTypes.COLLECT_ITEM:
                        return await CheckCollectItemObjectiveAsync(userId, objective);

                    case TaskObjectiveTypes.CURRENCY:
                        return await CheckCurrencyObjectiveAsync(userId, objective);

                    case TaskObjectiveTypes.REACH_LEVEL:
                        return await CheckLevelObjectiveAsync(userId, objective);

                    default:
                        _logger.LogWarning("未支持的任务目标类型: {ObjectiveType}", objective.objective_type);
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查目标完成状态失败，用户ID: {UserId}, 目标类型: {ObjectiveType}",
                    userId, objective.objective_type);
                return false;
            }
        }

        /// <summary>
        /// 检查击杀怪物目标
        /// </summary>
        private async Task<bool> CheckKillMonsterObjectiveAsync(int userId, task_objective objective)
        {
            try
            {
                int killCount;

                if (string.IsNullOrEmpty(objective.target_id))
                {
                    // 击杀任意怪物 - 只统计未结算的记录
                    killCount = await _db.Queryable<monster_kill_record>()
                        .Where(mkr => mkr.user_id == userId)
                        .CountAsync();
                }
                else
                {
                    // 击杀指定怪物 - 只统计未结算的记录
                    if (!int.TryParse(objective.target_id, out int monsterId))
                    {
                        _logger.LogWarning("无效的怪物ID: {TargetId}", objective.target_id);
                        return false;
                    }

                    killCount = await _db.Queryable<monster_kill_record>()
                        .Where(mkr => mkr.user_id == userId &&
                                    mkr.monster_id == monsterId)
                        .CountAsync();
                }

                return killCount >= objective.target_amount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查击杀怪物目标失败，用户ID: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 检查收集道具目标
        /// </summary>
        private async Task<bool> CheckCollectItemObjectiveAsync(int userId, task_objective objective)
        {
            try
            {
                if (string.IsNullOrEmpty(objective.target_id))
                {
                    _logger.LogWarning("收集道具目标缺少道具ID");
                    return false;
                }

                // 计算可用数量：总数量 - 已结算数量
                var userItems = await _db.Queryable<user_item>()
                    .Where(ui => ui.user_id == userId &&
                               ui.item_id == objective.target_id &&
                               ui.item_pos != 3) // 排除已丢弃的道具
                    .ToListAsync();

                long availableCount = userItems.Sum(ui => ui.item_count - ui.settlement_num);
                return availableCount >= objective.target_amount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查收集道具目标失败，用户ID: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 检查货币目标
        /// </summary>
        private async Task<bool> CheckCurrencyObjectiveAsync(int userId, task_objective objective)
        {
            try
            {
                var user = await _db.Queryable<user>().Where(u => u.id == userId).FirstAsync();
                if (user == null)
                {
                    return false;
                }

                long currentAmount = 0;
                var currencyType = objective.target_id?.ToLower() ?? "gold";

                switch (currencyType)
                {
                    case "gold":
                    case "金币":
                        currentAmount = user.gold ?? 0;
                        break;
                    case "yuanbao":
                    case "元宝":
                        currentAmount = user.yuanbao ?? 0;
                        break;
                    case "crystal":
                    case "水晶":
                        currentAmount = user.crystal ?? 0;
                        break;
                    default:
                        _logger.LogWarning("未知的货币类型: {CurrencyType}", currencyType);
                        return false;
                }

                return currentAmount >= objective.target_amount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查货币目标失败，用户ID: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 检查等级目标
        /// </summary>
        private async Task<bool> CheckLevelObjectiveAsync(int userId, task_objective objective)
        {
            try
            {
                // 获取用户主战宠物的等级
                var mainPet = await _db.Queryable<user_pet>()
                    .Where(up => up.user_id == userId && up.is_main == true)
                    .FirstAsync();

                if (mainPet == null)
                {
                    _logger.LogWarning("用户没有主战宠物，用户ID: {UserId}", userId);
                    return false;
                }

                // 根据经验计算等级
                var currentLevel = await _levelService.CalculateLevelAsync(mainPet.exp ?? 0, "pet");

                return currentLevel >= objective.target_amount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查等级目标失败，用户ID: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 检查用户是否有一键完成道具
        /// </summary>
        private async Task<bool> CheckUserHasOneClickItemAsync(int userId)
        {
            try
            {
                // 这里需要根据实际的一键完成道具ID来检查
                // 假设一键完成道具的ID是 "one_click_item"
                var hasItem = await _db.Queryable<user_item>()
                    .Where(ui => ui.user_id == userId &&
                               ui.item_id == "one_click_item" &&
                               ui.item_pos == 1 &&
                               ui.item_count > 0)
                    .AnyAsync();

                return hasItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查一键完成道具失败，用户ID: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 获取目标的当前进度数量（直接查询实际数据）
        /// </summary>
        private async Task<int> GetObjectiveCurrentAmountAsync(int userId, task_objective objective)
        {
            try
            {
                switch (objective.objective_type)
                {
                    case TaskObjectiveTypes.KILL_MONSTER:
                        return await GetKillMonsterCurrentAmountAsync(userId, objective);

                    case TaskObjectiveTypes.COLLECT_ITEM:
                        return await GetCollectItemCurrentAmountAsync(userId, objective);

                    case TaskObjectiveTypes.CURRENCY:
                        return await GetCurrencyCurrentAmountAsync(userId, objective);

                    case TaskObjectiveTypes.REACH_LEVEL:
                        return await GetLevelCurrentAmountAsync(userId, objective);

                    default:
                        _logger.LogWarning("未支持的任务目标类型: {ObjectiveType}", objective.objective_type);
                        return 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取目标当前进度失败，用户ID: {UserId}, 目标类型: {ObjectiveType}",
                    userId, objective.objective_type);
                return 0;
            }
        }

        /// <summary>
        /// 获取击杀怪物任务的当前进度
        /// </summary>
        private async Task<int> GetKillMonsterCurrentAmountAsync(int userId, task_objective objective)
        {
            try
            {
                if (string.IsNullOrEmpty(objective.target_id))
                {
                    // 击杀任意怪物 - 计算 kill_quantity - settlement_quantity 的总和
                    var records = await _db.Queryable<monster_kill_record>()
                        .Where(mkr => mkr.user_id == userId)
                        .ToListAsync();

                    return records.Sum(mkr => Math.Max(0, mkr.kill_quantity - mkr.settlement_quantity));
                }
                else
                {
                    // 击杀指定怪物 - 计算 kill_quantity - settlement_quantity
                    if (!int.TryParse(objective.target_id, out int monsterId))
                    {
                        return 0;
                    }

                    var record = await _db.Queryable<monster_kill_record>()
                        .Where(mkr => mkr.user_id == userId && mkr.monster_id == monsterId)
                        .FirstAsync();

                    if (record == null)
                    {
                        return 0;
                    }

                    return Math.Max(0, record.kill_quantity - record.settlement_quantity);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取击杀怪物进度失败，用户ID: {UserId}", userId);
                return 0;
            }
        }

        /// <summary>
        /// 获取收集道具任务的当前进度
        /// </summary>
        private async Task<int> GetCollectItemCurrentAmountAsync(int userId, task_objective objective)
        {
            try
            {
                if (string.IsNullOrEmpty(objective.target_id))
                {
                    return 0;
                }

                // 计算可用数量：总数量 - 已结算数量
                var userItems = await _db.Queryable<user_item>()
                    .Where(ui => ui.user_id == userId &&
                               ui.item_id == objective.target_id &&
                               ui.item_pos != 3) // 排除已丢弃的道具
                    .ToListAsync();

                long availableCount = userItems.Sum(ui => ui.item_count - ui.settlement_num);
                return (int)Math.Max(0, availableCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取收集道具进度失败，用户ID: {UserId}", userId);
                return 0;
            }
        }

        /// <summary>
        /// 获取货币任务的当前进度
        /// </summary>
        private async Task<int> GetCurrencyCurrentAmountAsync(int userId, task_objective objective)
        {
            try
            {
                var user = await _db.Queryable<user>().Where(u => u.id == userId).FirstAsync();
                if (user == null)
                {
                    return 0;
                }

                var currencyType = objective.target_id?.ToLower() ?? "gold";

                switch (currencyType)
                {
                    case "gold":
                    case "金币":
                        return (int)(user.gold ?? 0);
                    case "yuanbao":
                    case "元宝":
                        return user.yuanbao ?? 0;
                    case "crystal":
                    case "水晶":
                        return user.crystal ?? 0;
                    default:
                        return 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取货币进度失败，用户ID: {UserId}", userId);
                return 0;
            }
        }

        /// <summary>
        /// 获取等级任务的当前进度
        /// </summary>
        private async Task<int> GetLevelCurrentAmountAsync(int userId, task_objective objective)
        {
            try
            {
                // 获取用户主战宠物的等级
                var mainPet = await _db.Queryable<user_pet>()
                    .Where(up => up.user_id == userId && up.is_main == true)
                    .FirstAsync();

                if (mainPet == null)
                {
                    return 0;
                }

                // 根据经验计算等级
                var currentLevel = await _levelService.CalculateLevelAsync(mainPet.exp ?? 0, "pet");

                return currentLevel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级进度失败，用户ID: {UserId}", userId);
                return 0;
            }
        }

        #region 任务结算相关方法

        /// <summary>
        /// 更新任务结算状态
        /// </summary>
        private async Task UpdateTaskSettlementStatusAsync(int userId, string taskId)
        {
            try
            {
                // 获取任务的所有目标
                var objectives = await _db.Queryable<task_objective>()
                    .Where(to => to.task_id == taskId)
                    .ToListAsync();

                foreach (var objective in objectives)
                {
                    switch (objective.objective_type)
                    {
                        case TaskObjectiveTypes.KILL_MONSTER:
                            await UpdateKillMonsterSettlementAsync(userId, objective);
                            break;

                        case TaskObjectiveTypes.COLLECT_ITEM:
                            await UpdateCollectItemSettlementAsync(userId, objective);
                            break;

                        // 其他类型的任务不需要更新结算状态
                        default:
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务结算状态失败，用户ID: {UserId}, 任务ID: {TaskId}", userId, taskId);
                throw;
            }
        }

        /// <summary>
        /// 更新击杀怪物任务的结算状态
        /// </summary>
        private async Task UpdateKillMonsterSettlementAsync(int userId, task_objective objective)
        {
            try
            {
                if (string.IsNullOrEmpty(objective.target_id))
                {
                    // 击杀任意怪物 - 更新所有怪物的结算数量
                    var records = await _db.Queryable<monster_kill_record>()
                        .Where(mkr => mkr.user_id == userId)
                        .ToListAsync();

                    int remainingAmount = objective.target_amount;
                    foreach (var record in records.OrderBy(r => r.kill_time))
                    {
                        if (remainingAmount <= 0) break;

                        int availableAmount = record.kill_quantity - record.settlement_quantity;
                        if (availableAmount > 0)
                        {
                            int settlementAmount = Math.Min(availableAmount, remainingAmount);

                            await _db.Updateable<monster_kill_record>()
                                .SetColumns(mkr => new monster_kill_record
                                {
                                    settlement_quantity = record.settlement_quantity + settlementAmount
                                })
                                .Where(mkr => mkr.record_id == record.record_id)
                                .ExecuteCommandAsync();

                            remainingAmount -= settlementAmount;
                        }
                    }
                }
                else
                {
                    // 击杀指定怪物
                    if (int.TryParse(objective.target_id, out int monsterId))
                    {
                        var record = await _db.Queryable<monster_kill_record>()
                            .Where(mkr => mkr.user_id == userId && mkr.monster_id == monsterId)
                            .FirstAsync();

                        if (record != null)
                        {
                            int availableAmount = record.kill_quantity - record.settlement_quantity;
                            int settlementAmount = Math.Min(availableAmount, objective.target_amount);

                            if (settlementAmount > 0)
                            {
                                await _db.Updateable<monster_kill_record>()
                                    .SetColumns(mkr => new monster_kill_record
                                    {
                                        settlement_quantity = record.settlement_quantity + settlementAmount
                                    })
                                    .Where(mkr => mkr.record_id == record.record_id)
                                    .ExecuteCommandAsync();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新击杀怪物结算状态失败，用户ID: {UserId}, 目标ID: {TargetId}", userId, objective.target_id);
                throw;
            }
        }

        /// <summary>
        /// 更新收集道具任务的结算状态
        /// </summary>
        private async Task UpdateCollectItemSettlementAsync(int userId, task_objective objective)
        {
            try
            {
                if (string.IsNullOrEmpty(objective.target_id))
                {
                    return;
                }

                // 更新用户道具的结算数量
                var userItems = await _db.Queryable<user_item>()
                    .Where(ui => ui.user_id == userId &&
                               ui.item_id == objective.target_id &&
                               ui.item_pos != 3) // 排除已丢弃的道具
                    .ToListAsync();

                int remainingAmount = objective.target_amount;
                foreach (var userItem in userItems)
                {
                    if (remainingAmount <= 0) break;

                    // 计算可结算的数量
                    long availableAmount = userItem.item_count - userItem.settlement_num;
                    int settlementAmount = (int)Math.Min(availableAmount, remainingAmount);

                    if (settlementAmount > 0)
                    {
                        // 更新结算数量
                        await _db.Updateable<user_item>()
                            .SetColumns(ui => new user_item
                            {
                                settlement_num = userItem.settlement_num + settlementAmount
                            })
                            .Where(ui => ui.id == userItem.id)
                            .ExecuteCommandAsync();

                        remainingAmount -= settlementAmount;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新收集道具结算状态失败，用户ID: {UserId}, 道具ID: {ItemId}", userId, objective.target_id);
                throw;
            }
        }

        #endregion

        // End of private helper methods
    }
}
