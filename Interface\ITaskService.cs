using WebApplication_HM.DTOs;
using WebApplication_HM.Models;

namespace WebApplication_HM.Interface
{
    /// <summary>
    /// 任务管理服务接口
    /// </summary>
    public interface ITaskService
    {
        #region 任务查询

        /// <summary>
        /// 获取用户任务列表
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns>任务列表</returns>
        Task<TaskListResponseDto> GetUserTaskListAsync(TaskListRequestDto request);

        /// <summary>
        /// 获取可接取任务列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>可接取任务列表</returns>
        Task<List<TaskInfoDto>> GetAvailableTasksAsync(int userId);

        /// <summary>
        /// 获取已接受任务列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>已接受任务列表</returns>
        Task<List<TaskInfoDto>> GetAcceptedTasksAsync(int userId);

        /// <summary>
        /// 获取用户进行中的任务
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>进行中任务列表</returns>
        Task<List<UserTaskDto>> GetInProgressTasksAsync(int userId);

        /// <summary>
        /// 获取用户已完成的任务
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>已完成任务列表</returns>
        Task<List<UserTaskDto>> GetCompletedTasksAsync(int userId, int pageIndex = 1, int pageSize = 20);

        /// <summary>
        /// 获取任务详情
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务详情</returns>
        Task<TaskDetailResponseDto> GetTaskDetailAsync(int userId, string taskId);

        /// <summary>
        /// 检查任务是否可接取
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="taskId">任务ID</param>
        /// <returns>检查结果</returns>
        Task<(bool CanAccept, string Message)> CheckTaskAcceptabilityAsync(int userId, string taskId);

        #endregion

        #region 任务操作

        /// <summary>
        /// 接取任务
        /// </summary>
        /// <param name="request">接取任务请求</param>
        /// <returns>操作结果</returns>
        Task<TaskOperationResultDto> AcceptTaskAsync(AcceptTaskRequestDto request);

        /// <summary>
        /// 完成任务
        /// </summary>
        /// <param name="request">完成任务请求</param>
        /// <returns>操作结果</returns>
        Task<TaskOperationResultDto> CompleteTaskAsync(CompleteTaskRequestDto request);

        /// <summary>
        /// 放弃任务
        /// </summary>
        /// <param name="request">放弃任务请求</param>
        /// <returns>操作结果</returns>
        Task<TaskOperationResultDto> AbandonTaskAsync(AbandonTaskRequestDto request);

        /// <summary>
        /// 刷新任务状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        Task<bool> RefreshTaskStatusAsync(int userId);

        #endregion

        #region 任务进度

        /// <summary>
        /// 更新任务进度
        /// </summary>
        /// <param name="request">进度更新请求</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateTaskProgressAsync(UpdateTaskProgressRequestDto request);

        /// <summary>
        /// 批量更新任务进度
        /// </summary>
        /// <param name="events">进度更新事件列表</param>
        /// <returns>更新结果</returns>
        Task<bool> BatchUpdateTaskProgressAsync(List<TaskProgressUpdateEventDto> events);

        /// <summary>
        /// 检查并自动完成任务
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>自动完成的任务列表</returns>
        Task<List<UserTaskDto>> CheckAndAutoCompleteTasksAsync(int userId);

        /// <summary>
        /// 更新等级任务进度
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newLevel">新等级</param>
        /// <returns>更新结果</returns>
        Task UpdateLevelTaskProgressAsync(int userId, int newLevel);

        #endregion
    }

    /// <summary>
    /// 任务配置服务接口
    /// </summary>
    public interface ITaskConfigService
    {
        #region 任务配置管理

        /// <summary>
        /// 获取任务配置
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务配置</returns>
        Task<task_config> GetTaskConfigAsync(string taskId);

        /// <summary>
        /// 获取所有激活的任务配置
        /// </summary>
        /// <returns>任务配置列表</returns>
        Task<List<task_config>> GetActiveTaskConfigsAsync();

        /// <summary>
        /// 创建任务配置
        /// </summary>
        /// <param name="taskConfig">任务配置</param>
        /// <returns>创建结果</returns>
        Task<bool> CreateTaskConfigAsync(task_config taskConfig);

        /// <summary>
        /// 更新任务配置
        /// </summary>
        /// <param name="taskConfig">任务配置</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateTaskConfigAsync(task_config taskConfig);

        /// <summary>
        /// 删除任务配置
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteTaskConfigAsync(string taskId);

        #endregion

        #region 任务目标管理

        /// <summary>
        /// 获取任务目标列表
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>目标列表</returns>
        Task<List<task_objective>> GetTaskObjectivesAsync(string taskId);

        /// <summary>
        /// 创建任务目标
        /// </summary>
        /// <param name="objective">任务目标</param>
        /// <returns>创建结果</returns>
        Task<bool> CreateTaskObjectiveAsync(task_objective objective);

        /// <summary>
        /// 更新任务目标
        /// </summary>
        /// <param name="objective">任务目标</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateTaskObjectiveAsync(task_objective objective);

        /// <summary>
        /// 删除任务目标
        /// </summary>
        /// <param name="objectiveId">目标ID</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteTaskObjectiveAsync(int objectiveId);

        #endregion

        #region 任务类型管理

        /// <summary>
        /// 获取任务类型配置
        /// </summary>
        /// <returns>类型配置列表</returns>
        Task<List<task_type_config>> GetTaskTypeConfigsAsync();

        /// <summary>
        /// 获取任务类型处理器
        /// </summary>
        /// <param name="objectiveType">目标类型</param>
        /// <returns>处理器类名</returns>
        Task<string> GetTaskTypeHandlerAsync(string objectiveType);

        #endregion
    }

    /// <summary>
    /// 任务奖励服务接口
    /// </summary>
    public interface ITaskRewardService
    {
        #region 奖励处理

        /// <summary>
        /// 解析奖励配置
        /// </summary>
        /// <param name="rewardConfig">奖励配置JSON</param>
        /// <returns>奖励列表</returns>
        Task<List<TaskRewardDto>> ParseRewardConfigAsync(string rewardConfig);

        /// <summary>
        /// 发放任务奖励
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="taskId">任务ID</param>
        /// <param name="rewards">奖励列表</param>
        /// <returns>发放结果</returns>
        Task<bool> GrantTaskRewardsAsync(int userId, string taskId, List<TaskRewardDto> rewards);

        /// <summary>
        /// 记录奖励发放日志
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="taskId">任务ID</param>
        /// <param name="rewards">奖励列表</param>
        /// <returns>记录结果</returns>
        Task<bool> LogTaskRewardsAsync(int userId, string taskId, List<TaskRewardDto> rewards);

        /// <summary>
        /// 获取用户奖励历史
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>奖励历史</returns>
        Task<List<task_reward_log>> GetUserRewardHistoryAsync(int userId, int pageIndex = 1, int pageSize = 20);

        #endregion

        #region 奖励验证

        /// <summary>
        /// 验证奖励配置
        /// </summary>
        /// <param name="rewardConfig">奖励配置</param>
        /// <returns>验证结果</returns>
        Task<(bool IsValid, string Message)> ValidateRewardConfigAsync(string rewardConfig);

        /// <summary>
        /// 检查用户是否可以获得奖励
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="rewards">奖励列表</param>
        /// <returns>检查结果</returns>
        Task<(bool CanReceive, string Message)> CheckRewardEligibilityAsync(int userId, List<TaskRewardDto> rewards);

        #endregion
    }

    /// <summary>
    /// 任务进度处理器接口
    /// </summary>
    public interface ITaskProgressHandler
    {
        /// <summary>
        /// 支持的目标类型
        /// </summary>
        string SupportedObjectiveType { get; }

        /// <summary>
        /// 检查进度
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="objective">任务目标</param>
        /// <returns>当前进度</returns>
        Task<int> CheckProgressAsync(int userId, task_objective objective);

        /// <summary>
        /// 更新进度
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="objective">任务目标</param>
        /// <param name="amount">增加数量</param>
        /// <returns>更新后的进度</returns>
        Task<int> UpdateProgressAsync(int userId, task_objective objective, int amount);
    }

    /// <summary>
    /// 任务处理器工厂接口
    /// </summary>
    public interface ITaskHandlerFactory
    {
        /// <summary>
        /// 获取指定类型的任务处理器
        /// </summary>
        ITaskProgressHandler GetHandler(string objectiveType);

        /// <summary>
        /// 获取所有已注册的任务处理器
        /// </summary>
        IEnumerable<ITaskProgressHandler> GetAllHandlers();

        /// <summary>
        /// 检查是否支持指定的任务类型
        /// </summary>
        bool IsSupported(string objectiveType);

        /// <summary>
        /// 获取所有支持的任务类型
        /// </summary>
        IEnumerable<string> GetSupportedObjectiveTypes();

        /// <summary>
        /// 验证目标完成
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="objective">任务目标</param>
        /// <returns>是否完成</returns>
        Task<bool> ValidateCompletionAsync(int userId, task_objective objective);
    }



    /// <summary>
    /// 游戏事件触发服务接口
    /// </summary>
    public interface IGameEventTriggerService
    {
        /// <summary>
        /// 触发怪物击杀事件
        /// </summary>
        Task<bool> OnMonsterKilledAsync(int userId, string monsterId, int killCount = 1);

        /// <summary>
        /// 触发道具获得事件
        /// </summary>
        Task<bool> OnItemObtainedAsync(int userId, string itemId, int amount);

        /// <summary>
        /// 触发金币变化事件
        /// </summary>
        Task<bool> OnGoldChangedAsync(int userId, long newGoldAmount);

        /// <summary>
        /// 触发钻石变化事件
        /// </summary>
        Task<bool> OnDiamondChangedAsync(int userId, int newDiamondAmount);

        /// <summary>
        /// 触发经验变化事件
        /// </summary>
        Task<bool> OnExperienceChangedAsync(int userId, long newExperienceAmount);

        /// <summary>
        /// 触发用户升级事件
        /// </summary>
        Task<bool> OnUserLevelUpAsync(int userId, int newLevel);

        /// <summary>
        /// 触发装备获得事件
        /// </summary>
        Task<bool> OnEquipmentObtainedAsync(int userId, string equipmentId, int amount = 1);

        /// <summary>
        /// 触发副本完成事件
        /// </summary>
        Task<bool> OnDungeonCompletedAsync(int userId, string dungeonId, int completionCount = 1);

        /// <summary>
        /// 触发VIP等级变化事件
        /// </summary>
        Task<bool> OnVipLevelChangedAsync(int userId, int newVipLevel);
    }
}
