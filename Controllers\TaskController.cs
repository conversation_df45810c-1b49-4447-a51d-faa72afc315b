using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Interface;
using WebApplication_HM.DTOs;
using WebApplication_HM.DTOs.Common;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 任务系统控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class TaskController : ControllerBase
    {
        private readonly ITaskService _taskService;
        private readonly ITaskConfigService _taskConfigService;
        private readonly ITaskRewardService _taskRewardService;
        private readonly IGameEventTriggerService _gameEventTrigger;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TaskController> _logger;

        public TaskController(
            ITaskService taskService,
            ITaskConfigService taskConfigService,
            ITaskRewardService taskRewardService,
            IGameEventTriggerService gameEventTrigger,
            IServiceProvider serviceProvider,
            ILogger<TaskController> logger)
        {
            _taskService = taskService;
            _taskConfigService = taskConfigService;
            _taskRewardService = taskRewardService;
            _gameEventTrigger = gameEventTrigger;
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        #region 任务查询

        /// <summary>
        /// 获取用户任务列表
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns>任务列表</returns>
        [HttpPost("list")]
        public async Task<ActionResult<ApiResult<TaskListResponseDto>>> GetTaskList([FromBody] TaskListRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResult<TaskListResponseDto>.CreateError("请求参数无效"));
                }

                var result = await _taskService.GetUserTaskListAsync(request);
                return Ok(ApiResult<TaskListResponseDto>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务列表失败，用户ID: {UserId}", request.UserId);
                return StatusCode(500, ApiResult<TaskListResponseDto>.CreateError("获取任务列表失败"));
            }
        }

        /// <summary>
        /// 获取可接取任务列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>可接取任务列表</returns>
        [HttpGet("available/{userId}")]
        public async Task<ActionResult<ApiResult<List<TaskInfoDto>>>> GetAvailableTasks(int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(ApiResult<List<TaskInfoDto>>.CreateError("用户ID无效"));
                }

                var result = await _taskService.GetAvailableTasksAsync(userId);
                return Ok(ApiResult<List<TaskInfoDto>>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可接取任务失败，用户ID: {UserId}", userId);
                return StatusCode(500, ApiResult<List<TaskInfoDto>>.CreateError("获取可接取任务失败"));
            }
        }

        /// <summary>
        /// 获取已接受任务列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>已接受任务列表</returns>
        [HttpGet("accepted/{userId}")]
        public async Task<ActionResult<ApiResult<List<TaskInfoDto>>>> GetAcceptedTasks(int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(ApiResult<List<TaskInfoDto>>.CreateError("用户ID无效"));
                }

                var result = await _taskService.GetAcceptedTasksAsync(userId);
                return Ok(ApiResult<List<TaskInfoDto>>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取已接受任务失败，用户ID: {UserId}", userId);
                return StatusCode(500, ApiResult<List<TaskInfoDto>>.CreateError("获取已接受任务失败"));
            }
        }

        /// <summary>
        /// 获取用户进行中的任务
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>进行中任务列表</returns>
        [HttpGet("in-progress/{userId}")]
        public async Task<ActionResult<ApiResult<List<UserTaskDto>>>> GetInProgressTasks(int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(ApiResult<List<UserTaskDto>>.CreateError("用户ID无效"));
                }

                var result = await _taskService.GetInProgressTasksAsync(userId);
                return Ok(ApiResult<List<UserTaskDto>>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取进行中任务失败，用户ID: {UserId}", userId);
                return StatusCode(500, ApiResult<List<UserTaskDto>>.CreateError("获取进行中任务失败"));
            }
        }

        /// <summary>
        /// 获取用户已完成的任务
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>已完成任务列表</returns>
        [HttpGet("completed/{userId}")]
        public async Task<ActionResult<ApiResult<List<UserTaskDto>>>> GetCompletedTasks(int userId, int pageIndex = 1, int pageSize = 20)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(ApiResult<List<UserTaskDto>>.CreateError("用户ID无效"));
                }

                var result = await _taskService.GetCompletedTasksAsync(userId, pageIndex, pageSize);
                return Ok(ApiResult<List<UserTaskDto>>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取已完成任务失败，用户ID: {UserId}", userId);
                return StatusCode(500, ApiResult<List<UserTaskDto>>.CreateError("获取已完成任务失败"));
            }
        }

        /// <summary>
        /// 获取任务详情
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务详情</returns>
        [HttpGet("detail/{userId}/{taskId}")]
        public async Task<ActionResult<ApiResult<TaskDetailResponseDto>>> GetTaskDetail(int userId, string taskId)
        {
            try
            {
                if (userId <= 0 || string.IsNullOrEmpty(taskId))
                {
                    return BadRequest(ApiResult<TaskDetailResponseDto>.CreateError("参数无效"));
                }

                var result = await _taskService.GetTaskDetailAsync(userId, taskId);
                return Ok(ApiResult<TaskDetailResponseDto>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务详情失败，用户ID: {UserId}, 任务ID: {TaskId}", userId, taskId);
                return StatusCode(500, ApiResult<TaskDetailResponseDto>.CreateError("获取任务详情失败"));
            }
        }

        /// <summary>
        /// 检查任务是否可接取
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="taskId">任务ID</param>
        /// <returns>检查结果</returns>
        [HttpGet("check-acceptability/{userId}/{taskId}")]
        public async Task<ActionResult<ApiResult<object>>> CheckTaskAcceptability(int userId, string taskId)
        {
            try
            {
                if (userId <= 0 || string.IsNullOrEmpty(taskId))
                {
                    return BadRequest(ApiResult<object>.CreateError("参数无效"));
                }

                var (canAccept, message) = await _taskService.CheckTaskAcceptabilityAsync(userId, taskId);
                var result = new { CanAccept = canAccept, Message = message };
                
                return Ok(ApiResult<object>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查任务可接取性失败，用户ID: {UserId}, 任务ID: {TaskId}", userId, taskId);
                return StatusCode(500, ApiResult<object>.CreateError("检查任务可接取性失败"));
            }
        }

        #endregion

        #region 任务操作

        /// <summary>
        /// 接取任务
        /// </summary>
        /// <param name="request">接取任务请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("accept")]
        public async Task<ActionResult<ApiResult<TaskOperationResultDto>>> AcceptTask([FromBody] AcceptTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResult<TaskOperationResultDto>.CreateError("请求参数无效"));
                }

                var result = await _taskService.AcceptTaskAsync(request);
                
                if (result.Success)
                {
                    return Ok(ApiResult<TaskOperationResultDto>.CreateSuccess(result));
                }
                else
                {
                    return BadRequest(ApiResult<TaskOperationResultDto>.CreateError(result.Message));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "接取任务失败，用户ID: {UserId}, 任务ID: {TaskId}", request.UserId, request.TaskId);
                return StatusCode(500, ApiResult<TaskOperationResultDto>.CreateError("接取任务失败"));
            }
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        /// <param name="request">完成任务请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("complete")]
        public async Task<ActionResult<ApiResult<TaskOperationResultDto>>> CompleteTask([FromBody] CompleteTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResult<TaskOperationResultDto>.CreateError("请求参数无效"));
                }

                var result = await _taskService.CompleteTaskAsync(request);
                
                if (result.Success)
                {
                    return Ok(ApiResult<TaskOperationResultDto>.CreateSuccess(result));
                }
                else
                {
                    return BadRequest(ApiResult<TaskOperationResultDto>.CreateError(result.Message));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成任务失败，用户ID: {UserId}, 任务ID: {TaskId}", request.UserId, request.TaskId);
                return StatusCode(500, ApiResult<TaskOperationResultDto>.CreateError("完成任务失败"));
            }
        }

        /// <summary>
        /// 放弃任务
        /// </summary>
        /// <param name="request">放弃任务请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("abandon")]
        public async Task<ActionResult<ApiResult<TaskOperationResultDto>>> AbandonTask([FromBody] AbandonTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResult<TaskOperationResultDto>.CreateError("请求参数无效"));
                }

                var result = await _taskService.AbandonTaskAsync(request);
                
                if (result.Success)
                {
                    return Ok(ApiResult<TaskOperationResultDto>.CreateSuccess(result));
                }
                else
                {
                    return BadRequest(ApiResult<TaskOperationResultDto>.CreateError(result.Message));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "放弃任务失败，用户ID: {UserId}, 任务ID: {TaskId}", request.UserId, request.TaskId);
                return StatusCode(500, ApiResult<TaskOperationResultDto>.CreateError("放弃任务失败"));
            }
        }

        /// <summary>
        /// 刷新任务状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        [HttpPost("refresh/{userId}")]
        public async Task<ActionResult<ApiResult<bool>>> RefreshTaskStatus(int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(ApiResult<bool>.CreateError("用户ID无效"));
                }

                var result = await _taskService.RefreshTaskStatusAsync(userId);
                return Ok(ApiResult<bool>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新任务状态失败，用户ID: {UserId}", userId);
                return StatusCode(500, ApiResult<bool>.CreateError("刷新任务状态失败"));
            }
        }

        #endregion

        #region 任务进度

        /// <summary>
        /// 更新任务进度
        /// </summary>
        /// <param name="request">进度更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPost("update-progress")]
        public async Task<ActionResult<ApiResult<bool>>> UpdateTaskProgress([FromBody] UpdateTaskProgressRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResult<bool>.CreateError("请求参数无效"));
                }

                var result = await _taskService.UpdateTaskProgressAsync(request);
                return Ok(ApiResult<bool>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务进度失败，用户ID: {UserId}, 目标类型: {ObjectiveType}", 
                    request.UserId, request.ObjectiveType);
                return StatusCode(500, ApiResult<bool>.CreateError("更新任务进度失败"));
            }
        }

        /// <summary>
        /// 批量更新任务进度
        /// </summary>
        /// <param name="events">进度更新事件列表</param>
        /// <returns>更新结果</returns>
        [HttpPost("batch-update-progress")]
        public async Task<ActionResult<ApiResult<bool>>> BatchUpdateTaskProgress([FromBody] List<TaskProgressUpdateEventDto> events)
        {
            try
            {
                if (events == null || !events.Any())
                {
                    return BadRequest(ApiResult<bool>.CreateError("事件列表不能为空"));
                }

                var result = await _taskService.BatchUpdateTaskProgressAsync(events);
                return Ok(ApiResult<bool>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新任务进度失败");
                return StatusCode(500, ApiResult<bool>.CreateError("批量更新任务进度失败"));
            }
        }

        /// <summary>
        /// 检查并自动完成任务
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>自动完成的任务列表</returns>
        [HttpPost("auto-complete/{userId}")]
        public async Task<ActionResult<ApiResult<List<UserTaskDto>>>> CheckAndAutoCompleteTasks(int userId)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(ApiResult<List<UserTaskDto>>.CreateError("用户ID无效"));
                }

                var result = await _taskService.CheckAndAutoCompleteTasksAsync(userId);
                return Ok(ApiResult<List<UserTaskDto>>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查并自动完成任务失败，用户ID: {UserId}", userId);
                return StatusCode(500, ApiResult<List<UserTaskDto>>.CreateError("检查并自动完成任务失败"));
            }
        }

        #endregion

        #region 奖励相关

        /// <summary>
        /// 获取用户奖励历史
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>奖励历史</returns>
        [HttpGet("reward-history/{userId}")]
        public async Task<ActionResult<ApiResult<object>>> GetRewardHistory(int userId, int pageIndex = 1, int pageSize = 20)
        {
            try
            {
                if (userId <= 0)
                {
                    return BadRequest(ApiResult<object>.CreateError("用户ID无效"));
                }

                var result = await _taskRewardService.GetUserRewardHistoryAsync(userId, pageIndex, pageSize);
                return Ok(ApiResult<object>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取奖励历史失败，用户ID: {UserId}", userId);
                return StatusCode(500, ApiResult<object>.CreateError("获取奖励历史失败"));
            }
        }

        #endregion

        #region 管理员API

        /// <summary>
        /// 手动刷新用户任务状态（简化版）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>刷新结果</returns>
        [HttpPost("admin/refresh/{userId}")]
        public async Task<IActionResult> RefreshUserTaskStatus(int userId)
        {
            try
            {
                _logger.LogInformation("手动刷新用户任务状态，用户ID: {UserId}", userId);

                var result = await _taskService.RefreshTaskStatusAsync(userId);

                return Ok(ApiResult<object>.CreateSuccess(new
                {
                    Success = result,
                    Message = result ? "任务状态刷新成功" : "任务状态刷新失败"
                }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手动刷新任务状态失败，用户ID: {UserId}", userId);
                return StatusCode(500, ApiResult<object>.CreateError("手动刷新任务状态失败"));
            }
        }

        /// <summary>
        /// 触发游戏事件（测试用）
        /// </summary>
        /// <param name="request">事件请求</param>
        /// <returns>触发结果</returns>
        [HttpPost("admin/trigger-event")]
        public async Task<IActionResult> TriggerGameEvent([FromBody] TriggerEventRequest request)
        {
            try
            {
                bool success = false;
                string message = "";

                switch (request.EventType.ToLower())
                {
                    case "kill_monster":
                        success = await _gameEventTrigger.OnMonsterKilledAsync(request.UserId, request.TargetId, request.Amount);
                        message = $"触发击杀怪物事件: {request.TargetId} x{request.Amount}";
                        break;

                    case "item_obtained":
                        success = await _gameEventTrigger.OnItemObtainedAsync(request.UserId, request.TargetId, request.Amount);
                        message = $"触发道具获得事件: {request.TargetId} x{request.Amount}";
                        break;

                    case "gold_changed":
                        success = await _gameEventTrigger.OnGoldChangedAsync(request.UserId, request.Amount);
                        message = $"触发金币变化事件: {request.Amount}";
                        break;

                    case "level_up":
                        success = await _gameEventTrigger.OnUserLevelUpAsync(request.UserId, request.Amount);
                        message = $"触发用户升级事件: 等级{request.Amount}";
                        break;

                    default:
                        return BadRequest(ApiResult<object>.CreateError($"不支持的事件类型: {request.EventType}"));
                }

                if (success)
                {
                    return Ok(ApiResult<object>.CreateSuccess(new { Message = message }));
                }
                else
                {
                    return BadRequest(ApiResult<object>.CreateError("事件触发失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发游戏事件失败: {EventType}", request.EventType);
                return StatusCode(500, ApiResult<object>.CreateError("触发游戏事件失败"));
            }
        }

        #endregion
    }

    /// <summary>
    /// 触发事件请求
    /// </summary>
    public class TriggerEventRequest
    {
        public int UserId { get; set; }
        public string EventType { get; set; }
        public string TargetId { get; set; }
        public int Amount { get; set; }
    }
}
