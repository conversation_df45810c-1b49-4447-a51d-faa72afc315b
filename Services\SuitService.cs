using WebApplication_HM.Interface;
using WebApplication_HM.DTOs;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;
using System.Text.Json;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 套装服务实现
    /// </summary>
    public class SuitService : ISuitService
    {
        private readonly IEquipmentRepository _equipmentRepository;
        private readonly ILogger<SuitService> _logger;
        private readonly DbContext _dbContext;

        public SuitService(IEquipmentRepository equipmentRepository, ILogger<SuitService> logger, DbContext dbContext)
        {
            _equipmentRepository = equipmentRepository;
            _logger = logger;
            _dbContext = dbContext;
        }

        public async Task<List<SuitConfigDto>> GetAllSuitsAsync()
        {
            try
            {
                var suits = await _equipmentRepository.GetAllSuitConfigsAsync();
                var result = new List<SuitConfigDto>();

                foreach (var suit in suits)
                {
                    var suitDto = await MapToDtoAsync(suit);
                    result.Add(suitDto);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有套装配置失败");
                throw;
            }
        }

        public async Task<SuitConfigDto?> GetSuitByIdAsync(string suitId)
        {
            try
            {
                var suit = await _equipmentRepository.GetSuitConfigAsync(suitId);
                if (suit == null)
                    return null;

                return await MapToDtoAsync(suit);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取套装配置失败，套装ID: {SuitId}", suitId);
                throw;
            }
        }

        public async Task<List<SuitActivationDto>> GetPetSuitActivationsAsync(int petNo, int userId)
        {
            try
            {
                // 先获取宠物信息
                var pet = await _dbContext.Db.Queryable<user_pet>()
                    .Where(x => x.pet_no == petNo && x.user_id == userId)
                    .FirstAsync();
                if (pet == null)
                    return new List<SuitActivationDto>();

                // 查询宠物装备的套装信息
                var petEquipments = await _dbContext.Db.Queryable<user_equipment>()
                    .Where(x => x.pet_id == pet.id && x.user_id == userId && x.is_equipped == true)
                    .ToListAsync();

                // 使用更直接的方式查询宠物装备
                var equipmentsWithSuit = await GetPetEquipmentSuitInfoAsync(pet.id);

                // 按套装分组统计
                var suitGroups = equipmentsWithSuit
                    .Where(x => !string.IsNullOrEmpty(x.SuitId))
                    .GroupBy(x => x.SuitId)
                    .ToList();

                var activations = new List<SuitActivationDto>();

                foreach (var group in suitGroups)
                {
                    var suitConfig = await GetSuitByIdAsync(group.Key!);
                    if (suitConfig != null)
                    {
                        var activatedPieces = group.Count();
                        var activatedAttributes = suitConfig.Attributes
                            .Where(attr => attr.PieceCount <= activatedPieces)
                            .ToList();

                        activations.Add(new SuitActivationDto
                        {
                            SuitId = group.Key!,
                            SuitName = suitConfig.SuitName,
                            ActivatedPieces = activatedPieces,
                            TotalPieces = suitConfig.TotalPieces,
                            ActivatedAttributes = activatedAttributes,
                            AllAttributes = suitConfig.Attributes
                        });
                    }
                }

                return activations;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物套装激活状态失败，宠物ID: {petNo}", petNo);
                throw;
            }
        }

        /// <summary>
        /// 获取宠物装备的套装信息（优化版本）
        /// </summary>
        private async Task<List<EquipmentSuitInfo>> GetPetEquipmentSuitInfoAsync(int petId)
        {
            try
            {
                var result = new List<EquipmentSuitInfo>();

                // 查询宠物装备并关联装备详情获取套装ID
                var query = @"
                    SELECT ue.suit_id, ed.equip_id
                    FROM user_equipment ue
                    LEFT JOIN equipment_detail ed ON ue.equip_id = ed.equip_id
                    WHERE ue.pet_id = @petId
                        AND ue.is_equipped = 1";

                // 由于当前架构限制，使用Repository方式查询
                var equipments = await _equipmentRepository.GetUserEquipmentsAsync(0); // 临时实现

                // 实际应该通过petId过滤，这里先模拟
                foreach (var equipment in equipments.Where(e => e.pet_id == petId && e.is_equipped == true))
                {
                    // 通过装备详情获取套装信息
                    var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);

                    // 根据装备ID判断套装（基于原系统逻辑）
                    string? suitId = DetermineSuitIdByEquipId(equipment.equip_id);

                    if (!string.IsNullOrEmpty(suitId))
                    {
                        result.Add(new EquipmentSuitInfo
                        {
                            SuitId = suitId,
                            EquipId = equipment.equip_id
                        });
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物装备套装信息失败，宠物ID: {PetId}", petId);
                return new List<EquipmentSuitInfo>();
            }
        }

        /// <summary>
        /// 根据装备ID判断套装ID（基于原系统逻辑）
        /// </summary>
        private string? DetermineSuitIdByEquipId(string equipId)
        {
            // 基于装备ID前缀判断套装
            if (equipId.StartsWith("2017070101")) return "2017070101"; // 天魔套装
            if (equipId.StartsWith("2017063001")) return "2017063001"; // 自然套装
            if (equipId.StartsWith("20170612")) return "20170612";     // 黑白套装
            if (equipId.StartsWith("2016123001")) return "2016123001"; // 盛世套装
            if (equipId.StartsWith("2017051801")) return "2017051801"; // 龙鳞套装
            if (equipId.StartsWith("2017042901")) return "2017042901"; // 凤羽套装

            return null; // 非套装装备
        }

        private class EquipmentSuitInfo
        {
            public string SuitId { get; set; } = "";
            public string EquipId { get; set; } = "";
        }

        public async Task<Dictionary<string, double>> CalculateSuitAttributesAsync(int petNo, int userId)
        {
            var result = new Dictionary<string, double>
            {
                {"攻击", 0}, {"命中", 0}, {"防御", 0}, {"速度", 0},
                {"闪避", 0}, {"生命", 0}, {"魔法", 0}, {"加深", 0},
                {"抵消", 0}, {"吸血", 0}, {"吸魔", 0}
            };

            try
            {
                //  PetCalc.cs中的套装计算逻辑完整迁移
                var activations = await GetPetSuitActivationsAsync(petNo, userId);

                foreach (var activation in activations)
                {
                    foreach (var attr in activation.ActivatedAttributes)
                    {
                        if (result.ContainsKey(attr.AttributeType))
                        {
                            if (attr.IsPercentage)
                            {
                                // 百分比属性直接相加 ( 逻辑)
                                result[attr.AttributeType] += Convert.ToDouble(attr.AttributeValue);
                            }
                            else
                            {
                                // 数值型属性直接相加 ( 逻辑)
                                result[attr.AttributeType] += Convert.ToDouble(attr.AttributeValue);
                            }
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算套装属性失败，宠物ID: {petNo}", petNo);
                throw;
            }
        }

        private async Task<SuitConfigDto> MapToDtoAsync(suit_config suit)
        {
            var attributes = await _equipmentRepository.GetSuitAttributesAsync(suit.suit_id);

            return new SuitConfigDto
            {
                SuitId = suit.suit_id,
                SuitName = suit.suit_name,
                EquipmentList = ParseJsonArray(suit.equipment_list),
                Description = suit.description,
                TotalPieces = suit.total_pieces ?? 0,
                Attributes = attributes.Select(attr => new SuitAttributeDto
                {
                    PieceCount = attr.piece_count,
                    AttributeType = attr.attribute_type,
                    AttributeValue = attr.attribute_value,
                    IsPercentage = attr.is_percentage ?? false
                }).ToList()
            };
        }

        private List<string>? ParseJsonArray(string? jsonString)
        {
            if (string.IsNullOrEmpty(jsonString))
                return null;

            try
            {
                return JsonSerializer.Deserialize<List<string>>(jsonString);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析JSON数组失败: {JsonString}", jsonString);
                return null;
            }
        }

        private async Task<List<PetEquipmentWithSuit>> GetPetEquipmentsWithSuitAsync(int petId)
        {
            try
            {
                // 查询宠物装备并获取套装信息
                var equipments = await _equipmentRepository.GetUserEquipmentsAsync(0); // 临时用0，实际需要通过petId查询

                // 通过SQL直接查询更高效
                var query = @"
                    SELECT ue.suit_id
                    FROM user_equipment ue
                    WHERE ue.pet_id = @petId
                        AND ue.is_equipped = 1
                        AND ue.suit_id IS NOT NULL
                        AND ue.suit_id != ''";

                // 这里需要使用DbContext执行原生SQL
                var result = new List<PetEquipmentWithSuit>();

                // 简化实现：通过装备详情获取套装信息
                var petEquipments = await GetPetEquipmentsBySql(petId);

                foreach (var equipment in petEquipments)
                {
                    if (!string.IsNullOrEmpty(equipment.SuitId))
                    {
                        result.Add(new PetEquipmentWithSuit { SuitId = equipment.SuitId });
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物装备套装信息失败，宠物ID: {PetId}", petId);
                throw;
            }
        }

        /// <summary>
        /// 通过SQL查询宠物装备（包含套装信息）
        /// </summary>
        private async Task<List<PetEquipmentWithSuit>> GetPetEquipmentsBySql(int petId)
        {
            try
            {
                // 这里需要实际的数据库查询
                // 由于当前架构限制，先返回模拟数据
                var result = new List<PetEquipmentWithSuit>();

                // 实际实现应该是：
                // var sql = "SELECT suit_id FROM user_equipment WHERE pet_id = @petId AND is_equipped = 1";
                // var suitIds = await _dbContext.Db.Ado.SqlQueryAsync<string>(sql, new { petId });

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SQL查询宠物装备失败，宠物ID: {PetId}", petId);
                return new List<PetEquipmentWithSuit>();
            }
        }

        private class PetEquipmentWithSuit
        {
            public string? SuitId { get; set; }
        }
    }
}
