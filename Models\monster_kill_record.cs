using System;
using SqlSugar;

namespace WebApplication_HM.Models
{
    /// <summary>
    /// 怪物击杀记录表（简化版）
    /// </summary>
    [SugarTable("monster_kill_record")]
    public partial class monster_kill_record
    {
        public monster_kill_record()
        {
        }

        /// <summary>
        /// 记录ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public long record_id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int user_id { get; set; }

        /// <summary>
        /// 怪物ID
        /// </summary>
        public int monster_id { get; set; }

        /// <summary>
        /// 战斗ID（关联战斗记录）
        /// </summary>
        public string? battle_id { get; set; }

        /// <summary>
        /// 击杀时间
        /// </summary>
        public DateTime kill_time { get; set; }

        /// <summary>
        /// 击杀数量
        /// </summary>
        public int kill_quantity { get; set; }

        /// <summary>
        /// 结算数量
        /// </summary>
        public int settlement_quantity { get; set; }

    }

    /// <summary>
    /// 怪物击杀统计视图（简化版）
    /// </summary>
    [SugarTable("v_monster_kill_stats")]
    public partial class v_monster_kill_stats
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int user_id { get; set; }

        /// <summary>
        /// 怪物ID
        /// </summary>
        public int monster_id { get; set; }

        /// <summary>
        /// 击杀数量
        /// </summary>
        public int kill_count { get; set; }

        /// <summary>
        /// 首次击杀时间
        /// </summary>
        public DateTime first_kill_time { get; set; }

        /// <summary>
        /// 最后击杀时间
        /// </summary>
        public DateTime last_kill_time { get; set; }

        /// <summary>
        /// 击杀日期
        /// </summary>
        public DateTime kill_date { get; set; }
    }

    /// <summary>
    /// 用户每日击杀统计视图（简化版）
    /// </summary>
    [SugarTable("v_user_daily_kill_stats")]
    public partial class v_user_daily_kill_stats
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int user_id { get; set; }

        /// <summary>
        /// 击杀日期
        /// </summary>
        public DateTime kill_date { get; set; }

        /// <summary>
        /// 总击杀数
        /// </summary>
        public int total_kills { get; set; }

        /// <summary>
        /// 不同怪物数量
        /// </summary>
        public int unique_monsters { get; set; }
    }
}
