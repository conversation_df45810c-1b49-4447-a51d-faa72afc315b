<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .url-display {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API修复测试</h1>
        <p>测试修复后的API调用，确保URL中不再出现 [object Promise]</p>
        
        <div class="test-section">
            <h3>📋 基础API测试</h3>
            <button class="test-button" onclick="testGameHomeAPI()">测试 Game/home API</button>
            <button class="test-button" onclick="testPlayerInfoAPI()">测试 Player/GetPlayerInfo API</button>
            <button class="test-button" onclick="testAvailableTasksAPI()">测试 task/available API</button>
            <button class="test-button" onclick="testAcceptedTasksAPI()">测试 task/accepted API</button>
            <div id="basicResults"></div>
        </div>

        <div class="test-section">
            <h3>🎯 任务相关API测试</h3>
            <button class="test-button" onclick="testCompletedTasksAPI()">测试 task/completed API</button>
            <button class="test-button" onclick="testTaskDetailAPI()">测试 task/detail API</button>
            <div id="taskResults"></div>
        </div>

        <div class="test-section">
            <h3>🎮 宠物相关API测试</h3>
            <button class="test-button" onclick="testGetMainPetAPI()">测试 GetMainPet API</button>
            <button class="test-button" onclick="testGetUserPetsAPI()">测试 GetUserPets API</button>
            <div id="petResults"></div>
        </div>

        <div class="test-section">
            <h3>📦 背包相关API测试</h3>
            <button class="test-button" onclick="testPropAPI()">测试 Prop API</button>
            <button class="test-button" onclick="testPropUserAPI()">测试 Prop/user API (直接)</button>
            <div id="propResults"></div>
        </div>

        <div class="test-section">
            <h3>🛡️ 装备相关API测试</h3>
            <button class="test-button" onclick="testEquipmentAPI()">测试 Equipment API</button>
            <button class="test-button" onclick="testEquipmentUserAPI()">测试 Equipment/user API (直接)</button>
            <div id="equipmentResults"></div>
        </div>
    </div>

    <!-- 引入修复后的API适配器 -->
    <script src="../js/game-api-adapter.js"></script>
    
    <script>
        // 测试用户ID
        const TEST_USER_ID = 1;

        function addResult(containerId, message, type = 'info', data = null) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            
            let content = `<strong>${message}</strong>`;
            if (data) {
                content += `<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            resultDiv.innerHTML = content;
            
            container.appendChild(resultDiv);
            
            // 自动滚动到最新结果
            resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 测试 Game/home API
        async function testGameHomeAPI() {
            clearResults('basicResults');
            addResult('basicResults', '🔄 测试 Game/home API...', 'info');
            
            try {
                const result = await window.external.getGameHomeData();
                addResult('basicResults', '✅ Game/home API 调用成功', 'success', result);
            } catch (error) {
                addResult('basicResults', `❌ Game/home API 调用失败: ${error.message}`, 'error');
            }
        }

        // 测试 Player/GetPlayerInfo API
        async function testPlayerInfoAPI() {
            clearResults('basicResults');
            addResult('basicResults', '🔄 测试 Player/GetPlayerInfo API...', 'info');
            
            try {
                const result = await window.external.getPlayerInfo();
                addResult('basicResults', '✅ Player/GetPlayerInfo API 调用成功', 'success', JSON.parse(result));
            } catch (error) {
                addResult('basicResults', `❌ Player/GetPlayerInfo API 调用失败: ${error.message}`, 'error');
            }
        }

        // 测试 task/available API
        async function testAvailableTasksAPI() {
            clearResults('basicResults');
            addResult('basicResults', '🔄 测试 task/available API...', 'info');
            
            try {
                const result = await window.external.取所有可领任务();
                addResult('basicResults', '✅ task/available API 调用成功', 'success', JSON.parse(result));
            } catch (error) {
                addResult('basicResults', `❌ task/available API 调用失败: ${error.message}`, 'error');
            }
        }

        // 测试 task/accepted API
        async function testAcceptedTasksAPI() {
            clearResults('basicResults');
            addResult('basicResults', '🔄 测试 task/accepted API...', 'info');
            
            try {
                const result = await window.external.取玩家已领任务();
                addResult('basicResults', '✅ task/accepted API 调用成功', 'success', JSON.parse(result));
            } catch (error) {
                addResult('basicResults', `❌ task/accepted API 调用失败: ${error.message}`, 'error');
            }
        }

        // 测试 task/completed API
        async function testCompletedTasksAPI() {
            clearResults('taskResults');
            addResult('taskResults', '🔄 测试 task/completed API...', 'info');
            
            try {
                const response = await fetch(`/api/task/completed/${TEST_USER_ID}?pageIndex=1&pageSize=10`);
                const result = await response.json();
                addResult('taskResults', '✅ task/completed API 调用成功', 'success', result);
            } catch (error) {
                addResult('taskResults', `❌ task/completed API 调用失败: ${error.message}`, 'error');
            }
        }

        // 测试 task/detail API
        async function testTaskDetailAPI() {
            clearResults('taskResults');
            addResult('taskResults', '🔄 测试 task/detail API...', 'info');
            
            try {
                const result = await window.external.getTaskPage('TASK_001', false);
                addResult('taskResults', '✅ task/detail API 调用成功', 'success', result);
            } catch (error) {
                addResult('taskResults', `❌ task/detail API 调用失败: ${error.message}`, 'error');
            }
        }

        // 测试 GetMainPet API
        async function testGetMainPetAPI() {
            clearResults('petResults');
            addResult('petResults', '🔄 测试 GetMainPet API...', 'info');
            
            try {
                const result = await window.external.getMainPet();
                addResult('petResults', '✅ GetMainPet API 调用成功', 'success', JSON.parse(result));
            } catch (error) {
                addResult('petResults', `❌ GetMainPet API 调用失败: ${error.message}`, 'error');
            }
        }

        // 测试 GetUserPets API
        async function testGetUserPetsAPI() {
            clearResults('petResults');
            addResult('petResults', '🔄 测试 GetUserPets API...', 'info');
            
            try {
                const result = await window.external.getUserPets();
                addResult('petResults', '✅ GetUserPets API 调用成功', 'success', JSON.parse(result));
            } catch (error) {
                addResult('petResults', `❌ GetUserPets API 调用失败: ${error.message}`, 'error');
            }
        }

        // 测试 Prop API
        async function testPropAPI() {
            clearResults('propResults');
            addResult('propResults', '🔄 测试 Prop API...', 'info');
            
            try {
                const result = await window.external.updateIndex_Page(1, '');
                addResult('propResults', '✅ Prop API 调用成功', 'success');
            } catch (error) {
                addResult('propResults', `❌ Prop API 调用失败: ${error.message}`, 'error');
            }
        }

        // 测试 Equipment API
        async function testEquipmentAPI() {
            clearResults('equipmentResults');
            addResult('equipmentResults', '🔄 测试 Equipment API...', 'info');
            
            try {
                const result = await window.external.updateZBIndex_Page(1, '');
                addResult('equipmentResults', '✅ Equipment API 调用成功', 'success');
            } catch (error) {
                addResult('equipmentResults', `❌ Equipment API 调用失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后显示当前用户ID
        window.addEventListener('load', function() {
            const userId = getCurrentUserId();
            console.log('当前用户ID:', userId);
            console.log('getCurrentUserId函数类型:', typeof getCurrentUserId);

            // 显示用户ID信息
            const userIdInfo = document.createElement('div');
            userIdInfo.className = 'result info';
            userIdInfo.innerHTML = `<strong>当前用户ID: ${userId}</strong><br><small>函数类型: ${typeof getCurrentUserId}</small>`;
            document.querySelector('.container').insertBefore(userIdInfo, document.querySelector('.test-section'));

            // 测试URL构建
            const testUrl1 = `/api/Equipment/user/${userId}/unused`;
            const testUrl2 = `/api/Prop/user/${userId}/position/1`;

            console.log('测试URL 1:', testUrl1);
            console.log('测试URL 2:', testUrl2);

            // 显示URL测试结果
            const urlTestInfo = document.createElement('div');
            urlTestInfo.className = 'result info';
            urlTestInfo.innerHTML = `
                <strong>URL构建测试:</strong><br>
                <div class="url-display">Equipment API: ${testUrl1}</div>
                <div class="url-display">Prop API: ${testUrl2}</div>
            `;
            document.querySelector('.container').insertBefore(urlTestInfo, document.querySelector('.test-section'));
        });
    </script>
</body>
</html>
