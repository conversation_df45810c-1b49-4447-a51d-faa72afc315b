using WebApplication_HM.DTOs;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Models;

namespace WebApplication_HM.Interface
{
    /// <summary>
    /// 装备管理服务接口
    /// </summary>
    public interface IEquipmentService
    {
        // 基础装备管理
        Task<List<UserEquipmentDto>> GetUserEquipmentsAsync(int userId);
        Task<UserEquipmentDto?> GetEquipmentByIdAsync(int userEquipmentId);
        Task<ApiResult> AddEquipmentAsync(int userId, string equipTypeId);
        Task<ApiResult> DeleteEquipmentAsync(int userEquipmentId);
        
        // 装备穿戴系统
        Task<ApiResult> EquipToPetAsync(int userEquipmentId);
        Task<ApiResult> UnequipFromPetAsync(int userEquipmentId);
        Task<ApiResult> UnequipAllFromPetAsync(int petNo, int userId);
        Task<List<UserEquipmentDto>> GetPetEquipmentsAsync(int petNo, int userId);
        Task<List<UserEquipmentDto>> GetUnusedEquipmentsAsync(int userId);
        
        // 装备强化系统
        Task<ApiResult<StrengthenResult>> StrengthenEquipmentAsync(int userEquipmentId);
        Task<StrengthenCostDto> CalculateStrengthenCostAsync(int userEquipmentId);
        
        // 宝石镶嵌系统
        Task<ApiResult> EmbedGemstoneAsync(int userEquipmentId, string gemstoneTypeId, int position);
        Task<ApiResult> RemoveGemstoneAsync(int userEquipmentId, int position);
        Task<ApiResult> ClearAllGemstonesAsync(int userEquipmentId);
        Task<ApiResult> ExpandSlotAsync(int userEquipmentId);
        
        // 五行点化系统
        Task<ApiResult> TransformElementAsync(int userEquipmentId);
        
        // 装备分解系统
        Task<ApiResult<ResolveResult>> ResolveEquipmentAsync(int userEquipmentId);
        
        // 套装系统
        Task<List<SuitActivationDto>> GetPetSuitActivationsAsync(int petNo, int userId);

        // 属性计算系统
        Task<Dictionary<string, double>> CalculateEquipmentAttributesAsync(int petNo, int userId);
    }

    /// <summary>
    /// 宝石服务接口
    /// </summary>
    public interface IGemstoneService
    {
        Task<List<GemstoneConfigDto>> GetAllGemstonesAsync();
        Task<List<GemstoneConfigDto>> GetGemstonesByLevelAsync(int level);
        Task<GemstoneConfigDto?> GetGemstoneByTypeNameAsync(string typeName);
        Task<List<GemstoneDto>> GetEquipmentGemstonesAsync(int userEquipmentId);
        Task<bool> CanEmbedGemstoneAsync(int userEquipmentId, string gemstoneTypeName, int position);
    }

    /// <summary>
    /// 套装服务接口
    /// </summary>
    public interface ISuitService
    {
        Task<List<SuitConfigDto>> GetAllSuitsAsync();
        Task<SuitConfigDto?> GetSuitByIdAsync(string suitId);
        Task<List<SuitActivationDto>> GetPetSuitActivationsAsync(int petNo, int userId);
        Task<Dictionary<string, double>> CalculateSuitAttributesAsync(int petNo, int userId);
    }

    /// <summary>
    /// 装备属性计算服务接口
    /// </summary>
    public interface IEquipmentAttributeService
    {
        Task<Dictionary<string, double>> CalculateEquipmentAttributesAsync(int petNo, int userId);
        Task<Dictionary<string, double>> CalculateEquipmentAttributeAsync(int userEquipmentId);
        double CalculateEquipmentValue(string baseValue, int strengthenLevel, int elementBonus, bool isMainAttribute);
        Task<AttributeCalculationResult> GetDetailedAttributeCalculationAsync(int petNo, int userId);
    }

    /// <summary>
    /// 装备仓储接口
    /// </summary>
    public interface IEquipmentRepository
    {
        // 用户装备操作
        Task<List<user_equipment>> GetUserEquipmentsAsync(int userId);
        Task<user_equipment?> GetEquipmentByIdAsync(int userEquipmentId);
        Task<bool> AddEquipmentAsync(user_equipment equipment);
        Task<bool> UpdateEquipmentAsync(user_equipment equipment);
        Task<bool> DeleteEquipmentAsync(int userEquipmentId);
        
        // 装备详情操作
        Task<equipment_detail?> GetEquipmentDetailAsync(string equipId);
        Task<List<equipment_detail>> GetEquipmentDetailsByIdsAsync(List<string> equipIds);

        // 装备类型操作
        Task<equipment_type?> GetEquipmentTypeAsync(int typeId);
        
        // 宝石操作
        Task<List<equipment_gemstone>> GetEquipmentGemstonesAsync(int userEquipmentId);
        Task<bool> AddGemstoneAsync(equipment_gemstone gemstone);
        Task<bool> UpdateGemstoneAsync(equipment_gemstone gemstone);
        Task<bool> RemoveGemstoneAsync(int userEquipmentId, int position);
        Task<bool> ClearAllGemstonesAsync(int userEquipmentId);
        
        // 宝石配置操作
        Task<List<gemstone_config>> GetAllGemstoneConfigsAsync();
        Task<gemstone_config?> GetGemstoneConfigAsync(string typeName);
        
        // 套装操作
        Task<List<suit_config>> GetAllSuitConfigsAsync();
        Task<suit_config?> GetSuitConfigAsync(string suitId);
        Task<List<suit_attribute>> GetSuitAttributesAsync(string suitId);
        
        // 操作日志
        Task<bool> LogOperationAsync(equipment_operation_log log);
        Task<List<equipment_operation_log>> GetOperationLogsAsync(int userId, int? userEquipmentId = null);
    }
}
