<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>已完成任务调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .task-list {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            min-height: 100px;
        }
        .task-item {
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #eee;
            border-radius: 3px;
            background-color: #f9f9f9;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../js/game-api-adapter.js"></script>
</head>
<body>
    <div class="container">
        <h1>已完成任务功能调试测试</h1>
        
        <div class="test-section">
            <h3>1. 用户ID测试</h3>
            <button onclick="testGetUserId()">测试获取用户ID</button>
            <div id="userIdResults"></div>
        </div>

        <div class="test-section">
            <h3>2. API连接测试</h3>
            <button onclick="testApiConnection()">测试API连接</button>
            <div id="apiResults"></div>
        </div>

        <div class="test-section">
            <h3>3. 已完成任务API测试</h3>
            <button onclick="testCompletedTasksAPI()">测试已完成任务API</button>
            <div id="completedTasksResults"></div>
        </div>

        <div class="test-section">
            <h3>4. 模拟已完成任务加载</h3>
            <button onclick="simulateLoadCompletedTasks()">模拟加载已完成任务</button>
            <div class="task-list" id="simulatedTaskList">
                <div style="text-align:center;color:#999;">点击上方按钮开始测试</div>
            </div>
        </div>

        <div class="test-section">
            <h3>5. 控制台日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div class="log-output" id="logOutput"></div>
        </div>
    </div>

    <script>
        // 日志记录函数
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logOutput = document.getElementById('logOutput');
            const logEntry = `[${timestamp}] ${message}\n`;
            logOutput.textContent += logEntry;
            logOutput.scrollTop = logOutput.scrollHeight;
            
            console.log(message);
        }

        function clearLog() {
            document.getElementById('logOutput').textContent = '';
        }

        function addResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.innerHTML = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 测试获取用户ID
        function testGetUserId() {
            clearResults('userIdResults');
            addLog('🔍 开始测试获取用户ID...');
            
            try {
                // 测试同步函数
                if (typeof getCurrentUserId === 'function') {
                    const userId = getCurrentUserId();
                    addResult('userIdResults', `✅ getCurrentUserId(): ${userId}`, 'success');
                    addLog(`✅ getCurrentUserId() 返回: ${userId}`);
                } else {
                    addResult('userIdResults', '❌ getCurrentUserId 函数不存在', 'error');
                    addLog('❌ getCurrentUserId 函数不存在');
                }

                // 测试window.external
                if (window.external && window.external.getCurrentUserId) {
                    addResult('userIdResults', '✅ window.external.getCurrentUserId 存在', 'success');
                    addLog('✅ window.external.getCurrentUserId 存在');
                } else {
                    addResult('userIdResults', '⚠️ window.external.getCurrentUserId 不存在', 'warning');
                    addLog('⚠️ window.external.getCurrentUserId 不存在');
                }

            } catch (error) {
                addResult('userIdResults', `❌ 测试失败: ${error.message}`, 'error');
                addLog(`❌ 测试失败: ${error.message}`);
            }
        }

        // 测试API连接
        async function testApiConnection() {
            clearResults('apiResults');
            addLog('🌐 开始测试API连接...');
            
            try {
                // 测试基础API连接
                const response = await fetch('/api/task/available/1');
                addLog(`📡 API响应状态: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    addResult('apiResults', '✅ API服务器连接正常', 'success');
                    addLog('✅ API服务器连接正常');
                } else {
                    addResult('apiResults', `⚠️ API响应异常: ${response.status}`, 'warning');
                    addLog(`⚠️ API响应异常: ${response.status}`);
                }
            } catch (error) {
                addResult('apiResults', `❌ API连接失败: ${error.message}`, 'error');
                addLog(`❌ API连接失败: ${error.message}`);
            }
        }

        // 测试已完成任务API
        async function testCompletedTasksAPI() {
            clearResults('completedTasksResults');
            addLog('📋 开始测试已完成任务API...');
            
            try {
                const userId = getCurrentUserId ? getCurrentUserId() : 1;
                const apiUrl = `/api/task/completed/${userId}?pageIndex=1&pageSize=10`;
                
                addLog(`🌐 调用API: ${apiUrl}`);
                
                const response = await fetch(apiUrl);
                addLog(`📡 API响应状态: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const result = await response.json();
                    addLog(`📊 API返回数据: ${JSON.stringify(result, null, 2)}`);
                    
                    if (result.success) {
                        addResult('completedTasksResults', `✅ API调用成功，返回 ${result.data ? result.data.length : 0} 个已完成任务`, 'success');
                        addLog(`✅ API调用成功，返回 ${result.data ? result.data.length : 0} 个已完成任务`);
                    } else {
                        addResult('completedTasksResults', `⚠️ API返回失败: ${result.message}`, 'warning');
                        addLog(`⚠️ API返回失败: ${result.message}`);
                    }
                } else {
                    const errorText = await response.text();
                    addResult('completedTasksResults', `❌ API调用失败: ${response.status} ${response.statusText}`, 'error');
                    addLog(`❌ API调用失败: ${response.status} ${response.statusText}\n响应内容: ${errorText}`);
                }
            } catch (error) {
                addResult('completedTasksResults', `❌ 测试失败: ${error.message}`, 'error');
                addLog(`❌ 测试失败: ${error.message}`);
            }
        }

        // 模拟已完成任务加载
        async function simulateLoadCompletedTasks() {
            const taskList = document.getElementById('simulatedTaskList');
            addLog('🔄 开始模拟加载已完成任务...');
            
            try {
                taskList.innerHTML = '<div style="text-align:center;color:#999;">正在加载已完成任务...</div>';
                
                const userId = getCurrentUserId ? getCurrentUserId() : 1;
                const apiUrl = `/api/task/completed/${userId}?pageIndex=1&pageSize=50`;
                
                addLog(`🌐 调用API: ${apiUrl}`);
                
                const response = await fetch(apiUrl);
                addLog(`📡 API响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
                }
                
                const result = await response.json();
                addLog(`📊 API返回数据: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success && result.data && result.data.length > 0) {
                    let html = '';
                    for (let i = 0; i < result.data.length; i++) {
                        const task = result.data[i];
                        html += `
                            <div class="task-item">
                                <strong>${task.taskName || '未知任务'}</strong><br>
                                <small>描述: ${task.description || '无描述'}</small><br>
                                <small style="color:green;">✓ 已完成</small>
                            </div>
                        `;
                    }
                    taskList.innerHTML = html;
                    addLog(`✅ 成功加载 ${result.data.length} 个已完成任务`);
                } else {
                    taskList.innerHTML = '<div style="text-align:center;color:#999;">暂无已完成任务</div>';
                    addLog('⚠️ 没有已完成任务');
                }
                
            } catch (error) {
                taskList.innerHTML = `<div style="text-align:center;color:red;">加载失败: ${error.message}</div>`;
                addLog(`❌ 模拟加载失败: ${error.message}`);
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            addLog('🚀 页面加载完成，开始初始化测试...');
            
            // 显示当前环境信息
            addLog(`📍 当前URL: ${window.location.href}`);
            addLog(`🔧 jQuery版本: ${$ ? $.fn.jquery : '未加载'}`);
            addLog(`🎮 gameAPI存在: ${typeof gameAPI !== 'undefined'}`);
            addLog(`🆔 getCurrentUserId存在: ${typeof getCurrentUserId === 'function'}`);
            
            // 自动运行基础测试
            setTimeout(() => {
                testGetUserId();
            }, 500);
        });
    </script>
</body>
</html>
