<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>已完成任务功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .task-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 10px;
        }
        .task-item {
            padding: 8px;
            margin: 5px 0;
            background: #f8f9fa;
            border-left: 4px solid #4CAF50;
            cursor: pointer;
        }
        .task-item:hover {
            background: #e9ecef;
        }
        .task-detail {
            background: #fff;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 已完成任务功能测试</h1>
        
        <div class="test-section">
            <h3>📋 API测试</h3>
            <p>测试已完成任务相关的API端点</p>
            
            <button class="test-button" onclick="testGetCompletedTasks()">获取已完成任务</button>
            <button class="test-button" onclick="testTaskDetail()">获取任务详情</button>
            <button class="test-button" onclick="testTaskList()">获取任务列表</button>
            
            <div id="apiResults"></div>
        </div>

        <div class="test-section">
            <h3>🎮 前端功能测试</h3>
            <p>测试前端已完成任务显示功能</p>
            
            <button class="test-button" onclick="testFrontendDisplay()">测试前端显示</button>
            <button class="test-button" onclick="testTaskDetailModal()">测试任务详情弹窗</button>
            
            <div id="frontendResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 已完成任务列表</h3>
            <div id="completedTasksList" class="task-list">
                <p style="text-align:center;color:#999;">点击"获取已完成任务"按钮加载数据</p>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 任务详情</h3>
            <div id="taskDetailPanel" class="task-detail" style="display:none;">
                <h4 id="taskTitle">任务标题</h4>
                <p><strong>任务描述：</strong><span id="taskDescription"></span></p>
                <p><strong>完成时间：</strong><span id="taskCompletedTime"></span></p>
                <p><strong>完成次数：</strong><span id="taskCompletionCount"></span></p>
                <p><strong>任务奖励：</strong><span id="taskRewards"></span></p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/task';
        const TEST_USER_ID = 1; // 测试用户ID

        // 测试获取已完成任务
        async function testGetCompletedTasks() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<p>正在获取已完成任务...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/completed/${TEST_USER_ID}?pageIndex=1&pageSize=20`);
                const result = await response.json();
                
                if (result.success) {
                    resultsDiv.innerHTML = `
                        <div class="success result">
                            <strong>✅ 获取已完成任务成功！</strong><br>
                            获取到 ${result.data.length} 个已完成任务
                        </div>
                    `;
                    displayCompletedTasks(result.data);
                } else {
                    resultsDiv.innerHTML = `<div class="error result">❌ 获取失败: ${result.message}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error result">❌ 请求失败: ${error.message}</div>`;
            }
        }

        // 测试获取任务详情
        async function testTaskDetail() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<p>正在测试任务详情API...</p>';
            
            try {
                // 先获取一个任务ID
                const listResponse = await fetch(`${API_BASE}/completed/${TEST_USER_ID}?pageIndex=1&pageSize=1`);
                const listResult = await listResponse.json();
                
                if (listResult.success && listResult.data.length > 0) {
                    const taskId = listResult.data[0].taskInfo.taskId;
                    
                    const detailResponse = await fetch(`${API_BASE}/detail/${TEST_USER_ID}/${taskId}`);
                    const detailResult = await detailResponse.json();
                    
                    if (detailResult.success) {
                        resultsDiv.innerHTML = `
                            <div class="success result">
                                <strong>✅ 获取任务详情成功！</strong><br>
                                任务: ${detailResult.data.userTask.taskInfo.taskName}
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `<div class="error result">❌ 获取任务详情失败: ${detailResult.message}</div>`;
                    }
                } else {
                    resultsDiv.innerHTML = `<div class="error result">❌ 没有找到已完成的任务</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error result">❌ 请求失败: ${error.message}</div>`;
            }
        }

        // 测试任务列表API
        async function testTaskList() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<p>正在测试任务列表API...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/list`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        UserId: TEST_USER_ID,
                        PageIndex: 1,
                        PageSize: 10
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultsDiv.innerHTML = `
                        <div class="success result">
                            <strong>✅ 获取任务列表成功！</strong><br>
                            可接取任务: ${result.data.availableTasks.length}<br>
                            进行中任务: ${result.data.inProgressTasks.length}<br>
                            已完成任务: ${result.data.completedTasks.length}
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="error result">❌ 获取失败: ${result.message}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error result">❌ 请求失败: ${error.message}</div>`;
            }
        }

        // 显示已完成任务列表
        function displayCompletedTasks(tasks) {
            const listDiv = document.getElementById('completedTasksList');
            
            if (!tasks || tasks.length === 0) {
                listDiv.innerHTML = '<p style="text-align:center;color:#999;">暂无已完成任务</p>';
                return;
            }
            
            let html = '';
            tasks.forEach((task, index) => {
                const taskInfo = task.taskInfo || {};
                const completedTime = task.completedAt ? new Date(task.completedAt).toLocaleString() : '未知时间';
                
                html += `
                    <div class="task-item" onclick="showTaskDetail('${taskInfo.taskId}')">
                        <strong>✅ ${taskInfo.taskName || '未知任务'}</strong><br>
                        <small>完成时间: ${completedTime} | 完成次数: ${task.completionCount || 1}</small>
                    </div>
                `;
            });
            
            listDiv.innerHTML = html;
        }

        // 显示任务详情
        async function showTaskDetail(taskId) {
            const detailPanel = document.getElementById('taskDetailPanel');
            
            try {
                const response = await fetch(`${API_BASE}/detail/${TEST_USER_ID}/${taskId}`);
                const result = await response.json();
                
                if (result.success && result.data.userTask) {
                    const task = result.data.userTask;
                    const taskInfo = task.taskInfo || {};
                    
                    document.getElementById('taskTitle').textContent = taskInfo.taskName || '未知任务';
                    document.getElementById('taskDescription').textContent = taskInfo.taskDescription || '暂无描述';
                    document.getElementById('taskCompletedTime').textContent = task.completedAt ? new Date(task.completedAt).toLocaleString() : '未知时间';
                    document.getElementById('taskCompletionCount').textContent = task.completionCount || 1;
                    document.getElementById('taskRewards').textContent = taskInfo.rewards || '暂无奖励信息';
                    
                    detailPanel.style.display = 'block';
                } else {
                    alert('获取任务详情失败');
                }
            } catch (error) {
                alert('获取任务详情失败: ' + error.message);
            }
        }

        // 测试前端显示功能
        function testFrontendDisplay() {
            const resultsDiv = document.getElementById('frontendResults');
            resultsDiv.innerHTML = `
                <div class="success result">
                    <strong>✅ 前端显示功能测试</strong><br>
                    已完成任务导航已添加到主页面<br>
                    包含以下功能：<br>
                    • 已完成任务列表显示<br>
                    • 任务详情查看<br>
                    • 完成时间和次数显示<br>
                    • 响应式设计
                </div>
            `;
        }

        // 测试任务详情弹窗
        function testTaskDetailModal() {
            const resultsDiv = document.getElementById('frontendResults');
            resultsDiv.innerHTML = `
                <div class="success result">
                    <strong>✅ 任务详情弹窗测试</strong><br>
                    功能包括：<br>
                    • 显示任务基本信息<br>
                    • 显示完成状态和时间<br>
                    • 显示任务奖励<br>
                    • 禁用操作按钮（已完成任务不可操作）
                </div>
            `;
        }
    </script>
</body>
</html>
