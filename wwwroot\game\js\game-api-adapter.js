/**
 * 游戏API适配器 - 将原有的window.external调用转换为HTTP API调用
 * 用于将单机游戏页面集成到Web项目中
 */

// 全局配置
const API_BASE_URL = '/api';

// 获取当前用户ID的函数（确保不被其他文件覆盖）
function getCurrentUserId() {
    // 优先从认证管理器获取
    if (window.authManager && window.authManager.isLoggedIn()) {
        const userId = window.authManager.getCurrentUserId();
        if (userId && userId > 0) {
            // 更新缓存
            gameCache.set('currentUserId', userId, 3600000);
            console.log('✅ 从认证管理器获取用户ID:', userId);
            return userId;
        }
    }

    // 从缓存获取
    const cachedUserId = gameCache.get('currentUserId');
    if (cachedUserId && cachedUserId > 0) {
        // 验证缓存的用户ID是否仍然有效
        if (window.authManager && window.authManager.isLoggedIn()) {
            const currentUserId = window.authManager.getCurrentUserId();
            if (currentUserId && currentUserId === cachedUserId) {
                return cachedUserId;
            } else {
                // 缓存的用户ID与当前登录用户不匹配，清除缓存
                gameCache.remove('currentUserId');
            }
        } else {
            return cachedUserId;
        }
    }

    // 从URL参数获取
    const urlParams = new URLSearchParams(window.location.search);
    const userIdFromUrl = urlParams.get('userId');
    if (userIdFromUrl) {
        const userId = parseInt(userIdFromUrl);
        if (userId && userId > 0) {
            gameCache.set('currentUserId', userId, 3600000); // 1小时缓存
            return userId;
        }
    }

    // 检查是否为开发环境
    const isDev = window.location.hostname === 'localhost' ||
                 window.location.hostname === '127.0.0.1' ||
                 window.location.hostname.includes('dev');

    if (isDev) {
        console.warn('⚠️ 开发环境：未找到有效的用户ID，使用默认值1');
        gameCache.set('currentUserId', 1, 3600000);
        return 1;
    } else {
        console.error('❌ 生产环境：用户未登录，无法获取用户ID');
        // 生产环境下跳转到登录页面
        redirectToLogin();
        return null;
    }
}

// 跳转到登录页面
function redirectToLogin() {
    const loginUrl = getLoginUrl();
    console.log('跳转到登录页面:', loginUrl);

    // 显示提示信息
    if (window.loadingManager) {
        window.loadingManager.showError('用户未登录，即将跳转到登录页面...');
    } else {
        alert('用户未登录，即将跳转到登录页面...');
    }

    // 延迟跳转，让用户看到提示信息
    setTimeout(() => {
        window.location.href = loginUrl;
    }, 2000);
}

// 获取登录页面URL
function getLoginUrl() {
    // 1. 检查是否有配置的登录URL
    if (window.LOGIN_URL) {
        return window.LOGIN_URL;
    }

    // 2. 根据当前路径推断登录页面
    const currentPath = window.location.pathname;
    if (currentPath.includes('/game/pages/')) {
        return '/game/pages/Login.html';
    }

    // 3. 默认登录页面
    return '/Login.html';
}

// 设置当前用户ID（用于测试和登录）
function setCurrentUserId(userId) {
    gameCache.set('currentUserId', userId, 3600000);
    console.log('已设置用户ID:', userId);
}

// 模拟window.external对象
window.external = {
    // 基础检查方法
    check: function() {
        return "true";
    },

    // 玩家相关API
    async getPlayerInfo() {
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Player/GetPlayerInfo`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId
                })
            });
            const result = await response.json();

            // 转换为游戏页面期望的格式
            if (result.success && result.playerInfo) {
                const playerData = result.playerInfo;
                const gameFormat = {
                    账号: playerData.account || '',
                    昵称: playerData.nickname || '新玩家',
                    金币: playerData.gold || 0,
                    元宝: playerData.diamond || 0,
                    水晶: 0, // 暂时设为0
                    等级: playerData.level || 1,
                    VIP等级: playerData.level || 1,
                    自动购买体力丹: 0,
                    自动购买魔力丹: 0,
                    自动购买经验丹: 0,
                    时之券: 0,
                    体力上限: 100,
                    自动战斗次数: 0,
                    vip: playerData.level || 1,
                    sex: '保密',
                    论坛ID: playerData.account || '',
                    刷新次数: 0,
                    账号: playerData.account || '',
                    VIP权限: playerData.level >= 1 ? '已开通' : '未开通'
                };
                return JSON.stringify(gameFormat);
            }

            return JSON.stringify({});
        } catch (error) {
            console.error('获取玩家信息失败:', error);
            return JSON.stringify({});
        }
    },

    // 更新玩家信息页面
    async updatePlayerInfo_page() {
        try {
            const playerInfoJson = await this.getPlayerInfo();

            // 调用页面的showUserInfo函数显示玩家信息
            if (typeof showUserInfo === 'function') {
                showUserInfo(playerInfoJson);
            } else if (typeof parent.showUserInfo === 'function') {
                parent.showUserInfo(playerInfoJson);
            } else {
                // 如果没有showUserInfo函数，直接更新DOM元素
                const playerData = JSON.parse(playerInfoJson);
                this.updatePlayerInfoDOM(playerData);
            }

            return true;
        } catch (error) {
            console.error('更新玩家信息页面失败:', error);
            return false;
        }
    },

    // 直接更新DOM元素的方法
    updatePlayerInfoDOM(playerData) {
        const fieldMappings = {
            '账号': '.账号',
            '昵称': '.昵称',
            '金币': '.金币',
            '元宝': '.元宝',
            '水晶': '.水晶',
            '等级': '.等级',
            'VIP等级': '.VIP等级',
            '时之券': '.时之券',
            '体力上限': '.体力上限',
            'VIP权限': '.VIP权限'
        };

        Object.keys(fieldMappings).forEach(key => {
            const selector = fieldMappings[key];
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0 && playerData[key] !== undefined) {
                elements.forEach(el => {
                    el.textContent = playerData[key];
                });
            }
        });
    },

    // 获取游戏主页数据
    async getGameHomeData() {
        try {
            const userId = await getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Game/home/<USER>
            const result = await response.json();

            if (result.player && result.mainPet) {
                // 缓存玩家和宠物信息
                gameCache.set('playerInfo', result.player, 300000); // 5分钟缓存
                gameCache.set('mainPet', result.mainPet, 300000);

                return result;
            }

            return null;
        } catch (error) {
            console.error('获取游戏主页数据失败:', error);
            return null;
        }
    },

    // 初始化游戏主页
    async initGameHomePage() {
        try {
            const homeData = await this.getGameHomeData();
            if (homeData) {
                // 更新玩家信息显示
                if (homeData.player) {
                    this.updatePlayerInfoDisplay(homeData.player);
                }

                // 更新主战宠物显示
                if (homeData.mainPet) {
                    this.updateMainPetDisplay(homeData.mainPet);
                }

                // 触发页面更新事件
                if (typeof updateGameHome === 'function') {
                    updateGameHome(homeData);
                }
            }

            return true;
        } catch (error) {
            console.error('初始化游戏主页失败:', error);
            return false;
        }
    },

    // 更新玩家信息显示
    updatePlayerInfoDisplay(playerInfo) {
        // 更新金币显示
        const goldElements = document.querySelectorAll('.gold, .金币, #gold');
        goldElements.forEach(el => {
            if (playerInfo.gold !== undefined) {
                el.textContent = playerInfo.gold.toLocaleString();
            }
        });

        // 更新元宝显示
        const diamondElements = document.querySelectorAll('.diamond, .元宝, #diamond');
        diamondElements.forEach(el => {
            if (playerInfo.diamond !== undefined) {
                el.textContent = playerInfo.diamond.toLocaleString();
            }
        });

        // 更新昵称显示
        const nicknameElements = document.querySelectorAll('.nickname, .昵称, #nickname');
        nicknameElements.forEach(el => {
            if (playerInfo.nickname) {
                el.textContent = playerInfo.nickname;
            }
        });

        // 更新等级显示
        const levelElements = document.querySelectorAll('.level, .等级, #level');
        levelElements.forEach(el => {
            if (playerInfo.level !== undefined) {
                el.textContent = playerInfo.level;
            }
        });
    },

    // 更新主战宠物显示
    updateMainPetDisplay(petInfo) {
        // 设置全局主宠物ID变量
        if (petInfo.petNo !== undefined) {
            window.currentMainPetNo = petInfo.petNo;
            console.log('设置主宠物ID:', petInfo.petNo, '宠物名称:', petInfo.name);
        }

        // 更新宠物名称
        const petNameElements = document.querySelectorAll('.pet-name, .宠物名称, #petName');
        petNameElements.forEach(el => {
            if (petInfo.name) {
                el.textContent = petInfo.name;
            }
        });

        // 更新宠物等级
        const petLevelElements = document.querySelectorAll('.pet-level, .宠物等级, #petLevel');
        petLevelElements.forEach(el => {
            if (petInfo.level !== undefined) {
                el.textContent = `Lv.${petInfo.level}`;
            }
        });

        // 更新宠物血量
        const petHpElements = document.querySelectorAll('.pet-hp, .宠物血量, #petHp');
        petHpElements.forEach(el => {
            if (petInfo.hp !== undefined) {
                el.textContent = petInfo.hp;
            }
        });

        // 更新宠物魔法
        const petMpElements = document.querySelectorAll('.pet-mp, .宠物魔法, #petMp');
        petMpElements.forEach(el => {
            if (petInfo.mp !== undefined) {
                el.textContent = petInfo.mp;
            }
        });
    },

    // 宠物相关API
    async getUserPets() {
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Player/GetUserPets`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId
                })
            });
            const result = await response.json();
            return JSON.stringify(result.data?.pets || []);
        } catch (error) {
            console.error('获取宠物列表失败:', error);
            return JSON.stringify([]);
        }
    },

    async getMainPet() {
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Player/GetMainPet?userId=${userId}`);
            const result = await response.json();
            return JSON.stringify(result.data?.pets?.[0] || {});
        } catch (error) {
            console.error('获取主战宠物失败:', error);
            return JSON.stringify({});
        }
    },

    // 任务相关API
    async 取所有可领任务() {
        try {
            const userId = await getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/task/available/${userId}`);
            const result = await response.json();

            // 转换为前端期望的格式
            const tasks = (result.data || []).map(task => ({
                任务名: task.taskName || '',
                任务序号: task.taskId || ''
            }));

            return JSON.stringify(tasks);
        } catch (error) {
            console.error('获取可领任务失败:', error);
            return JSON.stringify([]);
        }
    },

    async 取玩家已领任务() {
        try {
            const userId = await getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/task/accepted/${userId}`);
            const result = await response.json();

            // 转换为前端期望的格式
            const tasks = (result.data || []).map(task => ({
                任务名: task.taskName || '',
                任务序号: task.taskId || ''
            }));

            return JSON.stringify(tasks);
        } catch (error) {
            console.error('获取已领任务失败:', error);
            return JSON.stringify([]);
        }
    },

    async getTaskPage(taskId, isAccepted) {
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/task/detail/${userId}/${taskId}`);
            const result = await response.json();
            const taskDetail = result.data || {};
            const taskInfo = taskDetail.userTask?.taskInfo || {};

            // 格式化任务目标
            const objectives = taskInfo.objectives || [];
            const objectiveText = objectives.map(obj => obj.objectiveDescription || '').join('\n');

            // 格式化任务奖励
            const rewards = taskInfo.rewards || [];
            const rewardText = rewards.map(reward =>
                `${reward.rewardType}|${reward.rewardValue}|${reward.rewardName || ''}|${reward.quantity || 1}`
            ).join('|');

            // 格式化任务进度
            const progress = taskDetail.userTask?.progress || [];
            const progressText = progress.length > 0 ?
                progress.map(p => `${p.currentValue || 0}/${p.targetValue || 1}`).join(', ') :
                '0/1';

            return JSON.stringify({
                任务名字: taskInfo.taskName || '',
                任务介绍: taskInfo.taskDescription || '',
                任务目标: objectiveText,
                任务奖励: rewardText,
                任务进度: progressText,
                循环任务: taskInfo.isRepeatable ? "1" : "0",
                位置: taskDetail.isAccepted || isAccepted
            });
        } catch (error) {
            console.error('获取任务详情失败:', error);
            return JSON.stringify({});
        }
    },

    async getTask(taskId) {
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/task/accept`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId,
                    TaskId: taskId
                })
            });
            const result = await response.json();
            return result.success;
        } catch (error) {
            console.error('接取任务失败:', error);
            return false;
        }
    },

    async deleteTask(taskId) {
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/task/abandon`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId,
                    TaskId: taskId
                })
            });
            const result = await response.json();
            return result.success;
        } catch (error) {
            console.error('放弃任务失败:', error);
            return false;
        }
    },

    async setTask(taskId) {
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/task/complete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId,
                    TaskId: taskId
                })
            });
            const result = await response.json();
            return result.success;
        } catch (error) {
            console.error('完成任务失败:', error);
            return false;
        }
    },

    // 战斗相关API
    async updateBattle_page(mapId, autoMode, isHell = false, isTower = false) {
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Player/CalculateBattle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId,
                    MapId: mapId,
                    AutoMode: autoMode === 1,
                    IsHellMode: isHell,
                    IsTowerMode: isTower
                })
            });
            const result = await response.json();
            
            // 触发战斗结果更新事件
            if (typeof updateBattleResult === 'function') {
                updateBattleResult(result.data);
            }
            
            return result.success;
        } catch (error) {
            console.error('战斗计算失败:', error);
            return false;
        }
    },

    // 道具和装备相关API
    async updateIndex_Page(page, searchName) {
        try {
            console.log(`获取背包数据 - 页码: ${page}, 搜索: ${searchName}`);

            // 直接调用PropController获取背包道具
            const userId = getCurrentUserId();
            const position = 1; // 1=背包
            const response = await fetch(`${API_BASE_URL}/Prop/user/${userId}/position/${position}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const items = await response.json();
            console.log('背包API返回数据:', items);

            if (Array.isArray(items)) {
                // 应用搜索过滤
                let filteredItems = items;
                if (searchName && searchName.trim()) {
                    const searchTerm = searchName.trim().toLowerCase();
                    filteredItems = items.filter(item =>
                        (item.itemName && item.itemName.toLowerCase().includes(searchTerm)) ||
                        (item.itemId && item.itemId.toLowerCase().includes(searchTerm))
                    );
                }

                // 应用分页
                const pageSize = 20; // 每页显示20个道具
                const startIndex = (page || 0) * pageSize;
                const pageItems = filteredItems.slice(startIndex, startIndex + pageSize);

                // 转换为页面期望的格式："名称,图标,ID,,数量|..."
                const formattedData = pageItems.map(item => {
                    const name = item.itemName || '未知道具';
                    const icon = item.itemIcon || 'default';
                    const itemId = item.itemId || '0'; // 使用itemId作为道具标识
                    const count = item.itemCount || 0;
                    return `${name},${icon},${itemId},,${count}`;
                }).join('|');

                console.log('格式化后的数据:', formattedData);

                // 调用页面的loadProp函数加载道具列表
                if (typeof loadProp === 'function') {
                    loadProp(formattedData);
                } else {
                    console.warn('loadProp函数未找到');
                }

                // 调用showUserInfo更新容量和分页信息
                if (typeof showUserInfo === 'function') {
                    const totalCapacity = 100; // 默认背包容量
                    const usedCapacity = filteredItems.length;
                    showUserInfo(totalCapacity, usedCapacity);
                } else {
                    console.warn('showUserInfo函数未找到');
                }

                return true;
            } else {
                console.error('背包API返回格式错误:', items);
                return false;
            }
        } catch (error) {
            console.error('获取背包数据失败:', error);

            // 显示错误提示
            if (typeof showBox === 'function') {
                showBox('获取背包数据失败，请稍后重试');
            }

            return false;
        }
    },

    async updateIndex_Page1(page, searchName) {
        // 装备列表的API调用，暂时复用道具API
        return this.updateIndex_Page(page, searchName);
    },

    // 道具缓存，避免重复API调用
    _itemCache: null,
    _itemCacheTime: 0,
    _itemCacheTimeout: 5000, // 5秒缓存

    // 辅助方法：通过ItemId查找ItemSeq（带缓存优化）
    async findItemSeqByItemId(itemId) {
        try {
            const now = Date.now();

            // 检查缓存是否有效
            if (this._itemCache && (now - this._itemCacheTime) < this._itemCacheTimeout) {
                console.log('使用缓存查找ItemSeq - ItemId:', itemId);
                const item = this._itemCache.find(item => item.itemId === itemId.toString());
                return item ? item.itemSeq : null;
            }

            console.log('从API获取道具列表 - ItemId:', itemId);
            const userId = getCurrentUserId();
            const position = 1; // 1=背包
            const response = await fetch(`${API_BASE_URL}/Prop/user/${userId}/position/${position}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                const items = await response.json();

                // 更新缓存
                this._itemCache = items;
                this._itemCacheTime = now;

                const item = items.find(item => item.itemId === itemId.toString());
                return item ? item.itemSeq : null;
            }
            return null;
        } catch (error) {
            console.error('查找ItemSeq失败:', error);
            return null;
        }
    },

    // 清除道具缓存（在道具使用、删除等操作后调用）
    clearItemCache() {
        console.log('清除道具缓存');
        this._itemCache = null;
        this._itemCacheTime = 0;
    },

    // 道具使用相关
    async useProp(itemId) {
        try {
            console.log(`使用道具 - ItemId: ${itemId}`);

            // 首先通过ItemId查找ItemSeq
            const itemSeq = await this.findItemSeqByItemId(itemId);
            if (!itemSeq) {
                throw new Error('找不到对应的道具');
            }

            // 直接调用PropController的使用道具接口
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Prop/use`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId,
                    ItemSeq: parseInt(itemSeq)
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('使用道具API返回:', result);

            if (result.success) {
                // 显示成功提示
                if (typeof showBox === 'function') {
                    showBox(result.message || '使用成功！');
                }

                // 清除道具缓存，确保下次获取最新数据
                this.clearItemCache();

                // 不在这里刷新背包，由调用方决定是否刷新
                // 避免重复调用 /api/Prop/user 接口
                // if (typeof getBag === 'function') {
                //     setTimeout(() => getBag(), 100);
                // }

                return true;
            } else {
                // 处理失败情况，提供用户友好的错误提示
                let errorMessage = result.message || '使用失败';

                // 检查是否为脚本相关错误，提供更友好的提示
                if (errorMessage.includes('未知的脚本指令') ||
                    errorMessage.includes('脚本执行失败') ||
                    errorMessage.includes('该道具无法直接使用')) {
                    errorMessage = '该道具不支持使用';
                }

                if (typeof showBox === 'function') {
                    showBox(errorMessage);
                }
                return false;
            }
        } catch (error) {
          
            if (typeof showBox === 'function') {
                showBox('该道具不支持直接使用！');
            }

            return false;
        }
    },

    async usePropXZ(itemId, selection) {
        // 使用自选道具
        return this.useProp(itemId);
    },

    async useZB(equipId) {
        // 使用装备（自动装备到主宠物）
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/equipment/equip`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: parseInt(userId),
                    UserEquipmentId: parseInt(equipId)
                })
            });
            const result = await response.json();
            return result.success ? '装备成功' : '装备失败';
        } catch (error) {
            console.error('装备失败:', error);
            return '装备失败';
        }
    },

    // 道具信息查询
    async readPropInfo(itemId, itemType, petId) {
        try {
            console.log(`获取道具信息 - ItemId: ${itemId}`);

            const response = await fetch(`${API_BASE_URL}/Prop/config/${itemId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                const config = await response.json();

                // 如果有详细的描述信息，直接使用它（包含HTML和颜色代码）
                if (config.Description && config.Description.length > 50) {
                    // 处理换行符，将 \r\n 转换为 <br/>
                    let description = config.Description.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>');
                    return description;
                } else {
                    // 如果没有详细描述，使用简单格式
                    return `道具名称: ${config.Name || '未知'}<br/>` +
                           `道具类型: ${config.Type || '未知'}<br/>` +
                           `道具描述: ${config.Description || '无描述'}<br/>` +
                           `道具价格: ${config.Price || 0}金币`;
                }
            } else {
                return `道具ID: ${itemId}<br/>详细信息加载失败`;
            }
        } catch (error) {
            console.error('获取道具信息失败:', error);
            return `道具ID: ${itemId}<br/>信息加载失败`;
        }
    },

    async readPropInfo1(itemId) {
        return this.readPropInfo(itemId, '道具', null);
    },

    // 道具操作
    async deleteProp(itemId) {
        try {
            console.log(`删除道具 - ItemId: ${itemId}`);

            // 确认删除
            if (typeof confirm === 'function') {
                if (!confirm('确定要丢弃这个道具吗？')) {
                    return false;
                }
            }

            // 首先通过ItemId查找ItemSeq
            const itemSeq = await this.findItemSeqByItemId(itemId);
            if (!itemSeq) {
                throw new Error('找不到对应的道具');
            }

            // 直接调用PropController的丢弃道具接口
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Prop/discard`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId,
                    ItemSeq: parseInt(itemSeq),
                    Reason: '用户主动丢弃'
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('删除道具API返回:', result);

            if (result.success) {
                if (typeof showBox === 'function') {
                    showBox('道具已丢弃');
                }

                // 清除道具缓存
                this.clearItemCache();

                // 刷新背包显示
                if (typeof getBag === 'function') {
                    setTimeout(() => getBag(), 100);
                }

                return true;
            } else {
                if (typeof showBox === 'function') {
                    showBox(result.message || '丢弃失败');
                }
                return false;
            }
        } catch (error) {
            console.error('删除道具失败:', error);

            if (typeof showBox === 'function') {
                showBox('删除道具失败：网络错误');
            }

            return false;
        }
    },

    async toDepot(itemId) {
        try {
            console.log(`放入仓库 - ItemId: ${itemId}`);

            // 通过ItemId查找ItemSeq
            const itemSeq = await this.findItemSeqByItemId(itemId);
            if (!itemSeq) {
                throw new Error('找不到对应的道具');
            }

            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Prop/move`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId,
                    ItemSeq: parseInt(itemSeq),
                    NewPosition: 2 // 2表示仓库
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('放入仓库API返回:', result);

            // 暂时总是返回成功，实际需要实现真正的仓库功能
            if (typeof showBox === 'function') {
                showBox('道具已经放入仓库！');
            }

            // 清除道具缓存
            this.clearItemCache();

            // 刷新背包显示
            if (typeof getBag === 'function') {
                setTimeout(() => getBag(), 100);
            }

            return true;
        } catch (error) {
            console.error('放入仓库失败:', error);

            if (typeof showBox === 'function') {
                showBox('无法存入道具,可能是背包+仓库数量超过背包容量！');
            }

            return false;
        }
    },

    // 邮件相关API (暂时返回空数据)
    getEmailList: function() {
        return "[]";
    },

    getEmail: function(id) {
        return "暂无邮件内容";
    },

    // 其他游戏功能的占位方法
    addHP: function() {
        console.log('恢复体力');
        return true;
    },

    updatePetInfo_page: function() {
        console.log('更新宠物信息页面');
        return true;
    },

    updatePlayerInfo_page: function() {
        console.log('更新玩家信息页面');
        return true;
    },

    updatePasture_page: function() {
        console.log('更新牧场页面');
        return true;
    },

    updateMalls_page: function() {
        console.log('更新商城页面');
        return true;
    },

    updateDepot_page: function() {
        console.log('更新仓库页面');
        return true;
    },

    updatePropMalls_page: function() {
        console.log('更新道具商城页面');
        return true;
    },

    updatePetMain_page: function() {
        console.log('更新宠物主页面');
        return true;
    },

    loadQHProp: function() {
        console.log('加载强化道具');
        return true;
    },

    updateZBWMain_page: function() {
        console.log('更新战斗屋页面');
        return true;
    },

    // 配置数据获取
    getsjson: function() {
        return JSON.stringify([]);
    },

    getejson: function() {
        return JSON.stringify([]);
    },

    getpjson: function() {
        return JSON.stringify([]);
    },

    getljson: function() {
        return JSON.stringify([]);
    },

    getSLjson: function() {
        return JSON.stringify([]);
    },

    getFormulaList: function() {
        return JSON.stringify([]);
    }
};

// 缓存机制
const gameCache = {
    set(key, data, ttl = 300000) { // 5分钟默认TTL
        try {
            localStorage.setItem(`game_${key}`, JSON.stringify({
                data,
                expires: Date.now() + ttl
            }));
        } catch (error) {
            console.warn('缓存设置失败:', error);
        }
    },

    get(key) {
        try {
            const item = localStorage.getItem(`game_${key}`);
            if (!item) return null;

            const parsed = JSON.parse(item);
            if (Date.now() > parsed.expires) {
                localStorage.removeItem(`game_${key}`);
                return null;
            }

            return parsed.data;
        } catch (error) {
            console.warn('缓存获取失败:', error);
            return null;
        }
    },

    clear(key) {
        try {
            if (key) {
                localStorage.removeItem(`game_${key}`);
            } else {
                // 清除所有游戏缓存
                Object.keys(localStorage).forEach(k => {
                    if (k.startsWith('game_')) {
                        localStorage.removeItem(k);
                    }
                });
            }
        } catch (error) {
            console.warn('缓存清除失败:', error);
        }
    }
};

// 工具函数
function showMessage(message) {
    if (typeof Alert === 'function') {
        Alert(message);
    } else {
        alert(message);
    }
}

// 兼容性：为旧代码提供getCurrentUserId函数
window.getCurrentUserId = getCurrentUserId;

// 初始化函数
async function initGameAdapter() {
    console.log('游戏API适配器已加载');

    try {
        // 延迟检查登录状态，给认证管理器时间初始化
        // 使用更智能的检查方式
        const checkWhenReady = () => {
            if (window.authManager && typeof window.authManager.isLoggedIn === 'function') {
                checkLoginStatus();
            } else {
                setTimeout(checkWhenReady, 200);
            }
        };

        setTimeout(checkWhenReady, 100);

        // 检查是否在游戏主页
        if (window.location.pathname.includes('Index.html') ||
            window.location.pathname.endsWith('/game') ||
            window.location.pathname.endsWith('/game/')) {

        }

        console.log('游戏API适配器初始化完成');

    } catch (error) {
        console.error('游戏API适配器初始化失败:', error);
    }
}

// 检查登录状态函数
function checkLoginStatus() {
    try {
        // 检查认证管理器是否存在并已初始化
        if (!window.authManager || typeof window.authManager.isLoggedIn !== 'function') {
            console.warn('⚠️ 认证管理器未完全加载，等待初始化...');
            // 再次延迟检查，但限制重试次数
            if (!checkLoginStatus.retryCount) {
                checkLoginStatus.retryCount = 0;
            }
            checkLoginStatus.retryCount++;

            if (checkLoginStatus.retryCount < 3) {
                setTimeout(checkLoginStatus, 500);
            } else {
                console.warn('⚠️ 认证管理器加载超时，跳过登录状态检查');
                // 不再重试，但不报错，因为可能是正常的加载顺序问题
            }
            return;
        }

        // 重置重试计数
        checkLoginStatus.retryCount = 0;

        // 检查登录状态
        const isLoggedIn = window.authManager.isLoggedIn();
        const currentUser = window.authManager.getCurrentUser();

        if (isLoggedIn && currentUser && currentUser.userId) {
            console.log('✅ 游戏API适配器 - 用户已登录:', {
                userId: currentUser.userId,
                username: currentUser.username,
                nickname: currentUser.nickname
            });

            // 更新缓存
            gameCache.set('currentUserId', currentUser.userId, 3600000);

            // 触发登录成功事件
            if (typeof window.onUserLoggedIn === 'function') {
                window.onUserLoggedIn(currentUser);
            }
        } else {
            // 检查是否为开发环境
            const isDev = window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1' ||
                         window.location.hostname.includes('dev');

            if (isDev) {
                console.warn('⚠️ 游戏API适配器 - 开发环境：用户未登录，某些功能可能无法正常使用');
            } else {
                console.warn('⚠️ 游戏API适配器 - 用户未登录，某些功能可能无法正常使用');
                // 生产环境可以选择跳转到登录页面
                // redirectToLogin();
            }
        }
    } catch (error) {
        console.error('检查登录状态时发生错误:', error);
    }
}

// 监听认证状态变化
function setupAuthStateListener() {
    // 监听认证管理器的状态变化
    if (window.authManager && typeof window.authManager.onStateChange === 'function') {
        window.authManager.onStateChange((isLoggedIn, user) => {
            if (isLoggedIn && user) {
                console.log('✅ 用户登录状态变化 - 已登录:', user);
                // 更新缓存
                gameCache.set('currentUserId', user.userId, 3600000);
                // 触发登录成功事件
                if (typeof window.onUserLoggedIn === 'function') {
                    window.onUserLoggedIn(user);
                }
            } else {
                console.log('⚠️ 用户登录状态变化 - 已登出');
                // 清除缓存
                gameCache.remove('currentUserId');
                // 触发登出事件
                if (typeof window.onUserLoggedOut === 'function') {
                    window.onUserLoggedOut();
                }
            }
        });
    }

    // 定期检查登录状态（作为备用机制）
    setInterval(() => {
        if (window.authManager) {
            const isLoggedIn = window.authManager.isLoggedIn();
            const cachedUserId = gameCache.get('currentUserId');

            if (isLoggedIn) {
                const currentUserId = window.authManager.getCurrentUserId();
                if (currentUserId && currentUserId !== cachedUserId) {
                    console.log('🔄 检测到用户ID变化，更新缓存:', currentUserId);
                    gameCache.set('currentUserId', currentUserId, 3600000);
                }
            } else if (cachedUserId) {
                console.log('🔄 检测到用户已登出，清除缓存');
                gameCache.remove('currentUserId');
            }
        }
    }, 30000); // 每30秒检查一次
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        initGameAdapter();
        setupAuthStateListener();
    });
} else {
    initGameAdapter();
    setupAuthStateListener();
}

// 保护getCurrentUserId函数不被覆盖
const _originalGetCurrentUserId = getCurrentUserId;
Object.defineProperty(window, 'getCurrentUserId', {
    value: _originalGetCurrentUserId,
    writable: false,
    configurable: false
});

// 导出全局函数供页面调用
window.gameAdapter = window.external;

// 导出背包相关API供页面调用
window.gameAPI = {
    // 背包相关方法（对接现有API）
    updateIndex_Page: (page, searchName) => gameAdapter.updateIndex_Page(page, searchName),
    updateIndex_Page1: (page, searchName) => gameAdapter.updateIndex_Page1(page, searchName),
    useProp: (itemId) => gameAdapter.useProp(itemId),
    deleteProp: (itemId) => gameAdapter.deleteProp(itemId),
    toDepot: (itemId) => gameAdapter.toDepot(itemId),
    readPropInfo: (itemId) => gameAdapter.readPropInfo(itemId),

    // 装备相关方法（对接现有API）
    updateZBIndex_Page: (page, searchName) => gameAdapter.updateZBIndex_Page(page, searchName),
    equipToPet: (userEquipmentId) => gameAdapter.equipToPet(userEquipmentId),
    unequipFromPet: (userEquipmentId) => gameAdapter.unequipFromPet(userEquipmentId),
    resolveEquipment: (userEquipmentId) => gameAdapter.resolveEquipment(userEquipmentId),
    deleteEquipment: (userEquipmentId) => gameAdapter.deleteEquipment(userEquipmentId),
    transformElement: (userEquipmentId) => gameAdapter.transformElement(userEquipmentId),
    getEquipmentInfo: (userEquipmentId) => gameAdapter.getEquipmentInfo(userEquipmentId),

    // 宠物相关方法
    getMainPet: () => gameAdapter.getMainPet(),

    // 宠物装备相关方法（新增）
    getPetEquipments: (petNo, userId) => gameAdapter.getPetEquipments(petNo, userId),
    getPetSuitActivations: (petNo, userId) => gameAdapter.getPetSuitActivations(petNo, userId),
    unequipAllFromPet: (petNo, userId) => gameAdapter.unequipAllFromPet(petNo, userId),
    getPetEquipmentAttributes: (petNo, userId) => gameAdapter.getPetEquipmentAttributes(petNo, userId),

    // 便捷方法（自动使用当前宠物和用户）
    getCurrentPetEquipments: () => gameAdapter.getCurrentPetEquipments(),
    getCurrentPetSuitActivations: () => gameAdapter.getCurrentPetSuitActivations(),
    unequipAllFromCurrentPet: () => gameAdapter.unequipAllFromCurrentPet(),
    getCurrentPetEquipmentAttributes: () => gameAdapter.getCurrentPetEquipmentAttributes(),

    // 地图相关方法
    getMapList: async (userId) => {
        try {
            const response = await fetch(`${API_BASE_URL}/Map/list/${userId}`);
            const result = await response.json();

            if (response.ok) {
                return result;
            } else {
                console.error('获取地图列表失败:', result.message);
                return { success: false, message: result.message || '获取地图列表失败' };
            }
        } catch (error) {
            console.error('获取地图列表异常:', error);
            return { success: false, message: '网络错误，请稍后重试' };
        }
    },

    getMapDetail: async (mapId, userId) => {
        try {
            const response = await fetch(`${API_BASE_URL}/Map/${mapId}/detail?userId=${userId}`);
            const result = await response.json();

            if (response.ok) {
                return result;
            } else {
                console.error('获取地图详情失败:', result.message);
                return { success: false, message: result.message || '获取地图详情失败' };
            }
        } catch (error) {
            console.error('获取地图详情异常:', error);
            return { success: false, message: '网络错误，请稍后重试' };
        }
    },

    getMapMonsters: async (mapId) => {
        try {
            const response = await fetch(`${API_BASE_URL}/Map/${mapId}/monsters`);
            const result = await response.json();

            if (response.ok) {
                return { success: true, monsters: result };
            } else {
                console.error('获取地图怪物失败:', result);
                return { success: false, message: '获取地图怪物失败' };
            }
        } catch (error) {
            console.error('获取地图怪物异常:', error);
            return { success: false, message: '网络错误，请稍后重试' };
        }
    },

    getMapDrops: async (mapId) => {
        try {
            const response = await fetch(`${API_BASE_URL}/Map/${mapId}/drops`);
            const result = await response.json();

            if (response.ok) {
                return { success: true, drops: result };
            } else {
                console.error('获取地图掉落失败:', result);
                return { success: false, message: '获取地图掉落失败' };
            }
        } catch (error) {
            console.error('获取地图掉落异常:', error);
            return { success: false, message: '网络错误，请稍后重试' };
        }
    },

    enterMap: async (mapId, userId, petId) => {
        try {
            const response = await fetch(`${API_BASE_URL}/Map/${mapId}/enter`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId,
                    PetId: petId
                })
            });

            const result = await response.json();

            if (response.ok) {
                return result;
            } else {
                console.error('进入地图失败:', result.message);
                return { success: false, message: result.message || '进入地图失败' };
            }
        } catch (error) {
            console.error('进入地图异常:', error);
            return { success: false, message: '网络错误，请稍后重试' };
        }
    },

    unlockMap: async (mapId, userId, unlockMethod = 'level') => {
        try {
            const response = await fetch(`${API_BASE_URL}/Map/${mapId}/unlock`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId,
                    UnlockMethod: unlockMethod
                })
            });

            const result = await response.json();

            if (response.ok) {
                return result;
            } else {
                console.error('解锁地图失败:', result.message);
                return { success: false, message: result.message || '解锁地图失败' };
            }
        } catch (error) {
            console.error('解锁地图异常:', error);
            return { success: false, message: '网络错误，请稍后重试' };
        }
    },

    updateMapProgress: async (mapId, userId, score, completionTimeSeconds) => {
        try {
            const response = await fetch(`${API_BASE_URL}/Map/${mapId}/progress`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: userId,
                    Score: score,
                    CompletionTimeSeconds: completionTimeSeconds
                })
            });

            const result = await response.json();

            if (response.ok) {
                return result;
            } else {
                console.error('更新地图进度失败:', result.message);
                return { success: false, message: result.message || '更新地图进度失败' };
            }
        } catch (error) {
            console.error('更新地图进度异常:', error);
            return { success: false, message: '网络错误，请稍后重试' };
        }
    },

    // 宠物相关API - 修复版本
    async getUserPets(userId) {
        try {
            console.log('获取用户宠物，用户ID:', userId);

            const response = await fetch(`${API_BASE_URL}/Player/GetUserPets`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    UserId: parseInt(userId)
                })
            });

            const result = await response.json();
            console.log('宠物API响应:', result);

            if (response.ok && result.success) {
                return {
                    success: true,
                    pets: result.pets || []
                };
            } else {
                console.error('获取宠物列表失败:', result.message);
                return {
                    success: false,
                    message: result.message || '获取宠物列表失败',
                    pets: []
                };
            }
        } catch (error) {
            console.error('获取用户宠物异常:', error);
            return {
                success: false,
                message: '网络错误，请稍后重试',
                pets: []
            };
        }
    },

    // 装备相关API方法
    async updateZBIndex_Page(page, searchName) {
        try {
            console.log('获取装备数据 - 页码:', page, '搜索:', searchName);

            const userId = getCurrentUserId();
            // 修改为获取未佩戴装备的接口
            const response = await fetch(`${API_BASE_URL}/Equipment/user/${userId}/unused`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('装备API返回数据:', result);

            if (result.success && Array.isArray(result.data)) {
                let equipments = result.data;

                // 过滤搜索结果
                if (searchName && searchName.trim() !== '') {
                    equipments = equipments.filter(item =>
                        (item.name && item.name.includes(searchName.trim())) ||
                        (item.equipName && item.equipName.includes(searchName.trim()))
                    );
                }

                // 转换为前端需要的格式
                const equipmentData = equipments.map(item => ({
                    itemSeq: item.id,
                    itemId: item.equipId,
                    itemName: item.name || item.equipName,
                    itemType: item.typeName || "装备", // 使用API返回的typeName字段
                    itemCount: 1,
                    itemIcon: item.icon,
                    description: item.description,
                    itemPrice: item.price || 0,
                    element: item.element || "无",
                    level: item.level || 1,
                    quality: item.quality || "普通"
                }));

                // 调用loadProp更新装备列表
                if (typeof loadProp === 'function') {
                    // 转换为loadProp需要的逗号分隔格式
                    // 格式：名称,图标ID,物品ID,?,数量,装备类型
                    const equipmentString = equipmentData.map(item =>
                        `${item.itemName},${item.itemIcon},${item.itemSeq},,${item.itemCount},${item.itemType}`
                    ).join('|');

                    console.log('装备数据字符串:', equipmentString);
                    loadProp(equipmentString);
                } else {
                    console.error('loadProp函数未找到');
                }

                return true;
            } else {
                console.error('装备API返回格式错误:', result);
                return false;
            }
        } catch (error) {
            console.error('获取装备数据失败:', error);

            // 显示错误提示
            if (typeof showBox === 'function') {
                showBox('获取装备数据失败，请稍后重试');
            }

            return false;
        }
    },

    async equipToPet(userEquipmentId) {
        try {
            console.log('装备穿戴请求 - 装备ID:', userEquipmentId, '(自动装备到主宠物)');

            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Equipment/equip`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userId: parseInt(userId),
                    userEquipmentId: parseInt(userEquipmentId)
                })
            });

            const result = await response.json();
            console.log('装备穿戴API响应:', result);

            return result;
        } catch (error) {
            console.error('装备穿戴失败:', error);
            return {
                success: false,
                message: '网络错误，装备穿戴失败'
            };
        }
    },

    async unequipFromPet(userEquipmentId) {
        try {
            console.log('装备卸下请求 - 装备ID:', userEquipmentId);

            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Equipment/unequip`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userId: parseInt(userId),
                    userEquipmentId: parseInt(userEquipmentId)
                })
            });

            const result = await response.json();
            console.log('装备卸下API响应:', result);

            return result;
        } catch (error) {
            console.error('装备卸下失败:', error);
            return {
                success: false,
                message: '网络错误，装备卸下失败'
            };
        }
    },

    async resolveEquipment(userEquipmentId) {
        try {
            console.log('装备分解请求 - 装备ID:', userEquipmentId);

            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Equipment/resolve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userId: parseInt(userId),
                    userEquipmentId: parseInt(userEquipmentId)
                })
            });

            const result = await response.json();
            console.log('装备分解API响应:', result);

            return result;
        } catch (error) {
            console.error('装备分解失败:', error);
            return {
                success: false,
                message: '网络错误，装备分解失败'
            };
        }
    },

    async deleteEquipment(userEquipmentId) {
        try {
            console.log('装备删除请求 - 装备ID:', userEquipmentId);

            const response = await fetch(`${API_BASE_URL}/Equipment/${userEquipmentId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            console.log('装备删除API响应:', result);

            return result;
        } catch (error) {
            console.error('装备删除失败:', error);
            return {
                success: false,
                message: '网络错误，装备删除失败'
            };
        }
    },

    async transformElement(userEquipmentId) {
        try {
            console.log('五行转换请求 - 装备ID:', userEquipmentId);

            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Equipment/element/transform`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userId: parseInt(userId),
                    userEquipmentId: parseInt(userEquipmentId)
                })
            });

            const result = await response.json();
            console.log('五行转换API响应:', result);

            return result;
        } catch (error) {
            console.error('五行转换失败:', error);
            return {
                success: false,
                message: '网络错误，五行转换失败'
            };
        }
    },

    async getEquipmentInfo(userEquipmentId) {
        try {
            console.log('获取装备详情 - 装备ID:', userEquipmentId);

            const response = await fetch(`${API_BASE_URL}/Equipment/${userEquipmentId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            console.log('装备详情API响应:', result);

            return result;
        } catch (error) {
            console.error('获取装备详情失败:', error);
            return {
                success: false,
                message: '网络错误，获取装备详情失败'
            };
        }
    },

    // 新增的宠物装备相关API方法
    async getPetEquipments(petNo, userId) {
        try {
            console.log('获取宠物装备 - 宠物编号:', petNo, '用户ID:', userId);

            const response = await fetch(`${API_BASE_URL}/Equipment/pet/${petNo}/user/${userId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            console.log('宠物装备API响应:', result);

            return result;
        } catch (error) {
            console.error('获取宠物装备失败:', error);
            return {
                success: false,
                message: '网络错误，获取宠物装备失败'
            };
        }
    },

    async getPetSuitActivations(petNo, userId) {
        try {
            console.log('获取套装激活状态 - 宠物编号:', petNo, '用户ID:', userId);

            const response = await fetch(`${API_BASE_URL}/Equipment/suit/activation/${petNo}/user/${userId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            console.log('套装激活状态API响应:', result);

            return result;
        } catch (error) {
            console.error('获取套装激活状态失败:', error);
            return {
                success: false,
                message: '网络错误，获取套装激活状态失败'
            };
        }
    },

    async unequipAllFromPet(petNo, userId) {
        try {
            console.log('批量卸装装备 - 宠物编号:', petNo, '用户ID:', userId);

            const response = await fetch(`${API_BASE_URL}/Equipment/unequip-all`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userId: parseInt(userId),
                    petNo: parseInt(petNo)
                })
            });

            const result = await response.json();
            console.log('批量卸装API响应:', result);

            return result;
        } catch (error) {
            console.error('批量卸装失败:', error);
            return {
                success: false,
                message: '网络错误，批量卸装失败'
            };
        }
    },

    async getPetEquipmentAttributes(petNo, userId) {
        try {
            console.log('获取宠物装备属性 - 宠物编号:', petNo, '用户ID:', userId);

            const response = await fetch(`${API_BASE_URL}/EquipmentAttribute/pet/${petNo}/user/${userId}/equipment-attributes`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            console.log('宠物装备属性API响应:', result);

            return result;
        } catch (error) {
            console.error('获取宠物装备属性失败:', error);
            return {
                success: false,
                message: '网络错误，获取宠物装备属性失败'
            };
        }
    },

    async getMainPet() {
        try {
            console.log('获取主宠物信息...');

            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Player/GetMainPet?userId=${userId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('主宠物API返回数据:', result);

            return result;
        } catch (error) {
            console.error('获取主宠物信息失败:', error);
            return {
                success: false,
                message: '获取主宠物信息失败'
            };
        }
    },

    // 高级封装方法（便捷调用）
    async getCurrentPetEquipments() {
        try {
            const userId = getCurrentUserId();
            const petNo = window.currentMainPetNo || 1;
            return await this.getPetEquipments(petNo, userId);
        } catch (error) {
            console.error('获取当前宠物装备失败:', error);
            return { success: false, message: '获取当前宠物装备失败' };
        }
    },

    async getCurrentPetSuitActivations() {
        try {
            const userId = getCurrentUserId();
            const petNo = window.currentMainPetNo || 1;
            return await this.getPetSuitActivations(petNo, userId);
        } catch (error) {
            console.error('获取当前宠物套装状态失败:', error);
            return { success: false, message: '获取当前宠物套装状态失败' };
        }
    },

    async unequipAllFromCurrentPet() {
        try {
            const userId = getCurrentUserId();
            const petNo = window.currentMainPetNo || 1;
            return await this.unequipAllFromPet(petNo, userId);
        } catch (error) {
            console.error('卸下当前宠物所有装备失败:', error);
            return { success: false, message: '卸下当前宠物所有装备失败' };
        }
    },

    async getCurrentPetEquipmentAttributes() {
        try {
            const userId = getCurrentUserId();
            const petNo = window.currentMainPetNo || 1;
            return await this.getPetEquipmentAttributes(petNo, userId);
        } catch (error) {
            console.error('获取当前宠物装备属性失败:', error);
            return { success: false, message: '获取当前宠物装备属性失败' };
        }
    },

    // 工具方法
    getCurrentUserId: getCurrentUserId,
    setCurrentUserId: setCurrentUserId,
    cache: gameCache
};
