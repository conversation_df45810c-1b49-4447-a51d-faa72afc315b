# 切换主宠后卸载装备跳回原主宠问题分析

## 问题描述

用户反馈：进入页面后，切换主宠，并卸载当前宠物的装备，会出现跳回刚进入页面时的主宠。

## 问题根因分析

### 1. 问题流程

```
1. 用户进入页面 → readPet() 加载初始主宠A
2. 用户切换主宠 → setPetWz() 切换到主宠B
3. 用户卸载装备 → 装备卸载成功
4. 系统调用 window.external.updatePetInfo_page()
5. 外部系统重新调用 chushihua() 或 readPet()
6. readPet() 重新加载宠物数据，找到状态为"0"的主宠A
7. 页面显示跳回到主宠A ❌
```

### 2. 核心问题代码

#### 装备卸载后的刷新调用
```javascript
// 装备卸载成功后
if (result.Success) {
    // ... 成功处理
    $element.html("");
    // 刷新装备显示 ← 问题触发点
    if (typeof window.external.updatePetInfo_page !== 'undefined') {
        window.external.updatePetInfo_page(); // ← 这里导致页面重新初始化
    }
}
```

#### readPet函数的主宠选择逻辑
```javascript
function readPet(json) {
    var j = $.parseJSON(json);
    allPetsData = j;
    $("#bottom").html("");
    
    for (i = 0; i < j.length; i++) {
        // 如果是主战宠物（状态为0）
        if (j[i].状态 == "0") {
            // 更新主宠物信息显示 ← 问题根源
            updateMainPet(j[i]); // 总是显示数据库中状态为0的主宠
            c = "";
        }
        // ...
    }
}
```

### 3. 问题分析

#### 数据状态不一致
```
前端状态: 用户切换到主宠B (setPetWz成功)
数据库状态: 主宠仍然是A (状态=0)
刷新后: readPet读取数据库，显示主宠A
```

#### 时序问题
```
1. setPetWz(B) → 前端显示切换到B
2. 卸载装备 → API调用成功
3. updatePetInfo_page() → 重新加载数据
4. readPet() → 从数据库读取，主宠仍是A
5. updateMainPet(A) → 显示跳回A
```

## 解决方案

### 方案1: 移除装备卸载后的页面刷新（推荐）

装备卸载只需要更新装备显示，不需要重新加载整个页面数据。

```javascript
// 装备卸载成功后
if (result.Success) {
    if (window.loadingManager) {
        window.loadingManager.showSuccess("装备卸下成功！");
    } else {
        window.parent.showBox("卸下装备成功！");
    }
    $element.html(""); // 清空装备槽显示
    
    // 移除页面刷新，只更新装备相关显示
    // if (typeof window.external.updatePetInfo_page !== 'undefined') {
    //     window.external.updatePetInfo_page(); // ← 注释掉这行
    // }
    
    // 可选：只刷新装备数据，不刷新宠物数据
    if (typeof window.external.refreshEquipmentOnly !== 'undefined') {
        window.external.refreshEquipmentOnly();
    }
}
```

### 方案2: 保持当前主宠状态的智能刷新

如果必须保留页面刷新，需要在刷新前保存当前主宠状态。

```javascript
// 在装备卸载前保存当前主宠ID
let currentDisplayPetId = Mid; // 当前显示的宠物ID

// 装备卸载成功后
if (result.Success) {
    // ... 成功处理
    $element.html("");
    
    // 智能刷新：保持当前主宠显示
    if (typeof window.external.updatePetInfo_page !== 'undefined') {
        // 保存当前状态
        window.tempCurrentPetId = currentDisplayPetId;
        window.external.updatePetInfo_page();
    }
}

// 修改readPet函数，支持保持当前显示
function readPet(json) {
    var j = $.parseJSON(json);
    allPetsData = j;
    $("#bottom").html("");
    
    // 检查是否需要保持当前显示的宠物
    let targetPetId = window.tempCurrentPetId || null;
    let targetPet = null;
    
    if (targetPetId) {
        targetPet = j.find(pet => pet.宠物序号 == targetPetId);
        window.tempCurrentPetId = null; // 清除临时状态
    }
    
    for (i = 0; i < j.length; i++) {
        var c = "yy";
        
        // 优先显示指定的宠物，否则显示主战宠物
        let shouldDisplay = targetPet ? 
            (j[i].宠物序号 == targetPet.宠物序号) : 
            (j[i].状态 == "0");
            
        if (shouldDisplay) {
            updateMainPet(j[i]);
            c = "";
        }
        // ... 其他代码
    }
}
```

### 方案3: 修改setPetWz确保数据库同步

确保切换主宠时数据库状态也同步更新。

```javascript
async function setPetWz(id, index) {
    pid = id;
    console.log(`开始切换宠物，ID: ${id}, 索引: ${index}`);

    // 先调用API设置主战宠物
    let switchSuccess = false;
    if (typeof window.external.switchPet === 'function') {
        try {
            console.log('调用API设置主战宠物...');
            switchSuccess = await window.external.switchPet(id);

            if (switchSuccess) {
                console.log('主战宠物设置成功');
                
                // 确保数据库状态同步更新
                if (typeof window.external.syncMainPetStatus !== 'undefined') {
                    await window.external.syncMainPetStatus(id);
                }
            } else {
                console.log('主战宠物设置失败');
                return;
            }
        } catch (error) {
            console.error('设置主战宠物时发生错误:', error);
            return;
        }
    }
    
    // ... 其他代码
}
```

## 推荐解决方案

### 立即修复：移除不必要的页面刷新

装备卸载操作本身不需要重新加载整个页面数据，只需要：
1. 清空对应的装备槽显示
2. 可选：更新战斗力计算
3. 可选：刷新装备相关的统计信息

```javascript
// 修改装备卸载成功后的处理
if (result.Success) {
    if (window.loadingManager) {
        window.loadingManager.showSuccess("装备卸下成功！");
    } else {
        window.parent.showBox("卸下装备成功！");
    }
    $element.html(""); // 清空装备槽
    
    // 重新计算战斗力（如果需要）
    // calculateBattlePower();
    
    // 不调用 updatePetInfo_page，避免页面重新初始化
}
```

### 长期优化：状态管理改进

1. **前端状态管理**：维护当前显示宠物的状态
2. **API同步**：确保切换主宠时数据库状态同步
3. **智能刷新**：页面刷新时保持用户当前的操作状态

## 技术细节

### 问题代码位置

1. **装备卸载**: 第1572-1574行
```javascript
if (typeof window.external.updatePetInfo_page !== 'undefined') {
    window.external.updatePetInfo_page(); // ← 问题触发
}
```

2. **背饰卸载**: 第1648-1650行
```javascript
if (typeof window.external.updatePetInfo_page !== 'undefined') {
    window.external.updatePetInfo_page(); // ← 问题触发
}
```

3. **readPet函数**: 第957-959行
```javascript
if (j[i].状态 == "0") {
    updateMainPet(j[i]); // ← 总是显示状态为0的主宠
    c = "";
}
```

### 影响范围

这个问题不仅影响装备卸载，还可能影响：
- 技能删除后的刷新（第810行）
- 技能升级后的刷新（第874行）
- 其他调用 `updatePetInfo_page()` 的操作

### 用户体验影响

1. **操作不连贯**：用户切换主宠后进行操作，结果跳回原主宠
2. **状态混乱**：前端显示与用户预期不符
3. **重复操作**：用户需要重新切换主宠才能继续操作

## 总结

问题的根本原因是装备卸载后调用 `window.external.updatePetInfo_page()` 导致页面重新初始化，而 `readPet()` 函数总是显示数据库中状态为"0"的主宠，忽略了用户当前的前端操作状态。

**最简单有效的解决方案**是移除装备卸载后的页面刷新调用，因为装备卸载操作本身不需要重新加载整个页面数据。
