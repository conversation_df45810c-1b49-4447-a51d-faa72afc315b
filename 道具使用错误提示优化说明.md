# 道具使用错误提示优化说明

## 问题描述

用户反馈：调用 `api/Prop/use` 接口时，返回的值是：
```json
{
  "success": false,
  "message": "未知的脚本指令: 成长加成*0.05"
}
```

当 `success` 为 `false` 时，应该提示"该道具不支持使用"，而不是显示技术性的错误信息。

## 问题分析

### 原有错误处理的问题

**API返回的技术性错误信息**:
- `"未知的脚本指令: 成长加成*0.05"`
- `"脚本执行失败"`
- `"该道具无法直接使用"`

**用户体验问题**:
1. **技术性错误**: 用户看到的是开发者才能理解的错误信息
2. **用户困惑**: 用户不知道"脚本指令"是什么意思
3. **体验不佳**: 错误信息不够友好和直观

### 错误处理流程分析

**当前流程**:
```
用户点击使用道具 → API调用 → 返回 {success: false, message: "未知的脚本指令: xxx"} → 
直接显示技术错误信息 → 用户困惑
```

**期望流程**:
```
用户点击使用道具 → API调用 → 返回 {success: false, message: "未知的脚本指令: xxx"} → 
转换为用户友好提示 → 显示"该道具不支持使用" → 用户理解
```

## 解决方案

### 1. 优化 gameAPI.useProp 错误处理

#### 修改前的代码

```javascript
if (result.success) {
    // 成功处理
    showBox(result.message || '使用成功！');
    return true;
} else {
    // 直接显示API返回的错误信息
    showBox(result.message || '使用失败');
    return false;
}
```

#### 修改后的代码

```javascript
if (result.success) {
    // 成功处理
    showBox(result.message || '使用成功！');
    return true;
} else {
    // 处理失败情况，提供用户友好的错误提示
    let errorMessage = result.message || '使用失败';
    
    // 检查是否为脚本相关错误，提供更友好的提示
    if (errorMessage.includes('未知的脚本指令') || 
        errorMessage.includes('脚本执行失败') ||
        errorMessage.includes('该道具无法直接使用')) {
        errorMessage = '该道具不支持使用';
    }
    
    showBox(errorMessage);
    return false;
}
```

### 2. 完善 Index.html 错误处理

#### 修改前的问题

```javascript
gameAPI.useProp(wpID).then(function(success) {
    if (success) {
        // 只处理成功情况
        $(dom).remove();
        // ...
    }
    // 缺少对 success === false 的处理
}).catch(function(error) {
    // 只处理异常情况
    showBox('使用道具失败');
});
```

#### 修改后的完善处理

```javascript
gameAPI.useProp(wpID).then(function(success) {
    if (success) {
        // 处理成功情况
        $(dom).remove();
        setTimeout(function() {
            updateBag();
        }, 300);
    } else {
        // 使用失败的情况（API返回success: false）
        // 错误信息已经在gameAPI.useProp中通过showBox显示了
        console.log('道具使用失败，可能是该道具不支持使用');
    }
}).catch(function(error) {
    // 处理网络异常等情况
    console.error('该道具');
    showBox('使用道具失败');
});
```

## 技术实现细节

### 1. 错误信息映射逻辑

```javascript
// 检查是否为脚本相关错误，提供更友好的提示
if (errorMessage.includes('未知的脚本指令') || 
    errorMessage.includes('脚本执行失败') ||
    errorMessage.includes('该道具无法直接使用')) {
    errorMessage = '该道具不支持使用';
}
```

**映射规则**:
- `"未知的脚本指令: xxx"` → `"该道具不支持使用"`
- `"脚本执行失败"` → `"该道具不支持使用"`
- `"该道具无法直接使用"` → `"该道具不支持使用"`

### 2. 错误处理层次

```
第1层: API层错误处理 (PropController)
第2层: 适配器层错误转换 (game-api-adapter.js)
第3层: 页面层错误处理 (Index.html)
```

**各层职责**:
- **API层**: 返回准确的技术错误信息
- **适配器层**: 将技术错误转换为用户友好信息
- **页面层**: 处理不同的返回状态

### 3. 状态处理完整性

```javascript
// 完整的状态处理
.then(function(success) {
    if (success) {
        // 处理成功状态
    } else {
        // 处理失败状态 (API返回success: false)
    }
})
.catch(function(error) {
    // 处理异常状态 (网络错误、解析错误等)
});
```

## 用户体验优化

### 1. 错误信息友好化

**优化前**:
- ❌ "未知的脚本指令: 成长加成*0.05"
- ❌ "脚本执行失败"
- ❌ "PropScriptResult.CreateFailure"

**优化后**:
- ✅ "该道具不支持使用"
- ✅ 简洁明了
- ✅ 用户容易理解

### 2. 操作反馈完整性

**优化前**:
- 成功: 有提示 ✅
- 失败: 技术错误 ❌
- 异常: 有提示 ✅

**优化后**:
- 成功: 有提示 ✅
- 失败: 友好提示 ✅
- 异常: 有提示 ✅

### 3. 用户理解度

**技术错误信息的问题**:
- 用户不知道什么是"脚本指令"
- 用户不理解"成长加成*0.05"的含义
- 用户可能认为是系统bug

**友好提示的优势**:
- 用户明确知道道具不能使用
- 用户不会误认为是系统错误
- 用户体验更流畅

## 适用场景

### 1. 道具脚本错误

**常见脚本错误**:
- 未知的脚本指令
- 脚本语法错误
- 脚本执行异常
- 道具配置错误

**统一提示**: "该道具不支持使用"

### 2. 其他业务错误

**保持原有提示**:
- "道具数量不足"
- "等级不够"
- "条件不满足"

**原则**: 只转换技术性错误，保留业务性错误

### 3. 网络异常

**保持原有处理**:
- "使用道具失败：网络错误"
- "使用道具失败"

## 扩展性考虑

### 1. 错误映射配置化

```javascript
// 可以考虑将错误映射配置化
const ERROR_MESSAGE_MAP = {
    'script_error': '该道具不支持使用',
    'level_insufficient': '等级不够',
    'item_insufficient': '道具数量不足'
};
```

### 2. 多语言支持

```javascript
// 支持多语言错误提示
const getLocalizedErrorMessage = (errorType) => {
    const messages = {
        'zh-CN': {
            'script_error': '该道具不支持使用',
            'network_error': '网络错误'
        },
        'en-US': {
            'script_error': 'This item is not usable',
            'network_error': 'Network error'
        }
    };
    return messages[currentLanguage][errorType];
};
```

### 3. 错误分类细化

```javascript
// 可以根据错误类型进行更细致的分类
const categorizeError = (errorMessage) => {
    if (errorMessage.includes('脚本')) return 'SCRIPT_ERROR';
    if (errorMessage.includes('网络')) return 'NETWORK_ERROR';
    if (errorMessage.includes('权限')) return 'PERMISSION_ERROR';
    return 'UNKNOWN_ERROR';
};
```

## 测试建议

### 1. 功能测试

```javascript
// 测试不同类型的错误响应
const testCases = [
    {
        response: { success: false, message: "未知的脚本指令: 成长加成*0.05" },
        expected: "该道具不支持使用"
    },
    {
        response: { success: false, message: "脚本执行失败" },
        expected: "该道具不支持使用"
    },
    {
        response: { success: false, message: "等级不够" },
        expected: "等级不够"
    }
];
```

### 2. 用户体验测试

- 测试用户对新错误提示的理解度
- 测试错误提示的显示时机
- 测试错误提示的显示效果

### 3. 兼容性测试

- 测试现有道具的使用功能
- 测试不同类型错误的处理
- 测试网络异常情况的处理

## 总结

通过这次优化，道具使用的错误提示变得更加用户友好：

### 核心改进

- **错误信息友好化**: 将技术性错误转换为用户易懂的提示
- **处理逻辑完善**: 补充了对 `success: false` 情况的处理
- **用户体验提升**: 用户不再看到困惑的技术错误信息

### 技术优势

- ✅ 保持API层的技术准确性
- ✅ 在适配器层进行用户友好转换
- ✅ 完善的错误状态处理
- ✅ 良好的扩展性和维护性

### 用户体验

- ✅ 错误提示简洁明了
- ✅ 用户容易理解操作结果
- ✅ 减少用户困惑和误解
- ✅ 提升整体使用体验

现在当用户使用不支持的道具时，会看到清晰的"该道具不支持使用"提示，而不是技术性的错误信息！
