<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Host 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .info-item {
            margin: 10px 0;
            padding: 8px;
            background-color: white;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .config-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #28a745;
            border-radius: 5px;
            background-color: #d4edda;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            margin: 10px 0;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Host 配置测试</h1>
        
        <div class="info-section">
            <h3>📍 当前页面信息</h3>
            <div class="info-item">
                <strong>完整URL:</strong> <span id="fullUrl"></span>
            </div>
            <div class="info-item">
                <strong>Protocol:</strong> <span id="protocol"></span>
            </div>
            <div class="info-item">
                <strong>Host:</strong> <span id="host"></span>
            </div>
            <div class="info-item">
                <strong>Hostname:</strong> <span id="hostname"></span>
            </div>
            <div class="info-item">
                <strong>Port:</strong> <span id="port"></span>
            </div>
            <div class="info-item">
                <strong>Pathname:</strong> <span id="pathname"></span>
            </div>
        </div>

        <div class="info-section">
            <h3>🌐 WebSocket URL 构建</h3>
            <div class="info-item">
                <strong>默认WebSocket URL:</strong> <span id="defaultWsUrl"></span>
            </div>
            <div class="info-item">
                <strong>当前自定义配置:</strong> <span id="customConfig"></span>
            </div>
            <div class="info-item">
                <strong>实际使用的WebSocket URL:</strong> <span id="actualWsUrl"></span>
            </div>
        </div>

        <div class="config-section">
            <h3>⚙️ 自定义 WebSocket Host 配置</h3>
            <p>如果您需要连接到不同的WebSocket服务器，可以设置自定义Host：</p>
            
            <div>
                <label for="customHost">自定义Host:</label>
                <input type="text" id="customHost" placeholder="例如: localhost:8080 或 ws.example.com">
                <button onclick="setCustomHost()">设置</button>
                <button onclick="clearCustomHost()">清除</button>
            </div>
            
            <div class="code-block">
                <strong>代码示例:</strong><br>
                // 在页面加载前设置自定义WebSocket主机<br>
                window.WEBSOCKET_HOST = 'localhost:8080';<br>
                // 或者<br>
                window.WEBSOCKET_HOST = 'ws.yourdomain.com';
            </div>
        </div>

        <div class="info-section">
            <h3>🧪 测试功能</h3>
            <button onclick="testWebSocketUrl()">测试WebSocket URL构建</button>
            <button onclick="testConnection()">测试WebSocket连接</button>
            <div id="testResults"></div>
        </div>

        <div class="info-section">
            <h3>📝 说明</h3>
            <p><strong>window.location.host</strong> 是浏览器自动提供的只读属性，包含当前页面的主机名和端口号。</p>
            <p><strong>不能直接修改</strong> window.location.host 的值，但可以通过以下方式自定义WebSocket服务器地址：</p>
            <ul>
                <li>设置 <code>window.WEBSOCKET_HOST</code> 全局变量</li>
                <li>修改 chat-websocket.js 中的URL构建逻辑</li>
                <li>使用环境变量或配置文件</li>
            </ul>
        </div>
    </div>

    <script>
        // 页面加载时显示当前信息
        function updatePageInfo() {
            document.getElementById('fullUrl').textContent = window.location.href;
            document.getElementById('protocol').textContent = window.location.protocol;
            document.getElementById('host').textContent = window.location.host;
            document.getElementById('hostname').textContent = window.location.hostname;
            document.getElementById('port').textContent = window.location.port || '(默认端口)';
            document.getElementById('pathname').textContent = window.location.pathname;
            
            // 构建WebSocket URL
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const defaultWsUrl = `${protocol}//${window.location.host}/ws/universal`;
            document.getElementById('defaultWsUrl').textContent = defaultWsUrl;
            
            // 检查自定义配置
            const customConfig = window.WEBSOCKET_HOST || '(未设置)';
            document.getElementById('customConfig').textContent = customConfig;
            
            // 实际使用的URL
            const actualHost = window.WEBSOCKET_HOST || window.location.host;
            const actualWsUrl = `${protocol}//${actualHost}/ws/universal`;
            document.getElementById('actualWsUrl').textContent = actualWsUrl;
        }

        // 设置自定义Host
        function setCustomHost() {
            const customHost = document.getElementById('customHost').value.trim();
            if (customHost) {
                window.WEBSOCKET_HOST = customHost;
                addResult('✅ 自定义Host已设置: ' + customHost, 'success');
                updatePageInfo();
            } else {
                addResult('❌ 请输入有效的Host地址', 'error');
            }
        }

        // 清除自定义Host
        function clearCustomHost() {
            delete window.WEBSOCKET_HOST;
            document.getElementById('customHost').value = '';
            addResult('✅ 自定义Host已清除', 'success');
            updatePageInfo();
        }

        // 测试WebSocket URL构建
        function testWebSocketUrl() {
            clearResults();
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.WEBSOCKET_HOST || window.location.host;
            const wsUrl = `${protocol}//${host}/ws/universal`;
            
            addResult('🌐 WebSocket URL构建测试:', 'info');
            addResult(`  - Protocol: ${window.location.protocol} → ${protocol}`, 'info');
            addResult(`  - Host: ${window.location.host}`, 'info');
            addResult(`  - 使用的Host: ${host}`, 'info');
            addResult(`  - 最终URL: ${wsUrl}`, 'success');
        }

        // 测试WebSocket连接
        function testConnection() {
            clearResults();
            addResult('🔄 开始测试WebSocket连接...', 'info');
            
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const host = window.WEBSOCKET_HOST || window.location.host;
                const wsUrl = `${protocol}//${host}/ws/universal`;
                
                addResult(`📡 尝试连接: ${wsUrl}`, 'info');
                
                const testWs = new WebSocket(wsUrl);
                
                testWs.onopen = function(event) {
                    addResult('✅ WebSocket连接成功！', 'success');
                    testWs.close();
                };
                
                testWs.onerror = function(error) {
                    addResult('❌ WebSocket连接失败: ' + error, 'error');
                };
                
                testWs.onclose = function(event) {
                    if (event.code === 1000) {
                        addResult('🔌 WebSocket连接已正常关闭', 'info');
                    } else {
                        addResult(`⚠️ WebSocket连接异常关闭: ${event.code} ${event.reason}`, 'error');
                    }
                };
                
                // 5秒后超时
                setTimeout(() => {
                    if (testWs.readyState === WebSocket.CONNECTING) {
                        testWs.close();
                        addResult('⏰ 连接超时', 'error');
                    }
                }, 5000);
                
            } catch (error) {
                addResult('❌ 测试失败: ' + error.message, 'error');
            }
        }

        // 添加测试结果
        function addResult(message, type) {
            const container = document.getElementById('testResults');
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        // 清空测试结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            updatePageInfo();
            
            // 每5秒更新一次信息（以防有动态变化）
            setInterval(updatePageInfo, 5000);
        });
    </script>
</body>
</html>
